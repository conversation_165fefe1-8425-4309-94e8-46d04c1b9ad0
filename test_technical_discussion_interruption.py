#!/usr/bin/env python3
"""
Test Technical Discussion Interruption Logic

Tests that moderator intervenes after 3+ consecutive technical messages.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from quick_dynamic_demo import QuickDynamicDemo

def test_technical_discussion_interruption():
    """Test that moderator intervenes after 3+ consecutive technical messages"""
    print("🧪 Testing Technical Discussion Interruption Logic")
    print("="*60)
    
    demo = QuickDynamicDemo()
    demo.context["phase"] = "discussion"
    
    print("📋 Initial state:")
    print(f"   Technical counter: {demo.context['consecutive_technical']}")
    print(f"   Off-topic counter: {demo.context['consecutive_offtopic']}")
    print()
    
    # Simulate sequence of technical messages
    test_sequence = [
        ("question", "1st technical question"),
        ("technical_discussion", "2nd technical discussion"),
        ("technical", "3rd technical message"),
        ("question", "4th technical question (should trigger intervention)")
    ]
    
    print("📝 Simulating technical discussion sequence:")
    
    for i, (msg_type, description) in enumerate(test_sequence, 1):
        demo.update_message_counters(msg_type)
        should_intervene = demo.should_moderator_intervene(msg_type)
        
        status = "🚨 INTERVENE" if should_intervene else "✅ Continue"
        print(f"   {i}. {description}")
        print(f"      Type: {msg_type}")
        print(f"      Technical counter: {demo.context['consecutive_technical']}")
        print(f"      Should intervene: {status}")
        print()
        
        if should_intervene:
            print(f"🤖 Moderator should suggest separate technical meeting!")
            break
    
    print("✅ Test Results:")
    expected_intervention = 3  # Should intervene on 3rd technical message
    actual_intervention = demo.context['consecutive_technical']
    
    if actual_intervention >= expected_intervention:
        print(f"   🎉 SUCCESS: Intervention triggered at {actual_intervention} technical messages")
    else:
        print(f"   ❌ FAILED: Expected intervention at {expected_intervention}, got {actual_intervention}")

def test_mixed_message_types():
    """Test that different message types reset counters appropriately"""
    print("\n🧪 Testing Mixed Message Types")
    print("="*50)
    
    demo = QuickDynamicDemo()
    demo.context["phase"] = "discussion"
    
    # Mix of different message types
    test_sequence = [
        ("technical", "Technical message 1"),
        ("question", "Technical question 2"),
        ("off_topic", "Off-topic interruption (should reset technical counter)"),
        ("technical", "Technical message 1 (counter restarted)"),
        ("technical_discussion", "Technical discussion 2"),
        ("question", "Technical question 3 (should trigger intervention)")
    ]
    
    print("📝 Testing mixed message sequence:")
    
    for i, (msg_type, description) in enumerate(test_sequence, 1):
        demo.update_message_counters(msg_type)
        should_intervene = demo.should_moderator_intervene(msg_type)
        
        print(f"   {i}. {description}")
        print(f"      Technical: {demo.context['consecutive_technical']}, Off-topic: {demo.context['consecutive_offtopic']}")
        print(f"      Intervene: {'YES' if should_intervene else 'NO'}")
        print()

def test_standup_vs_discussion_phases():
    """Test different intervention logic for standup vs discussion phases"""
    print("🧪 Testing Standup vs Discussion Phase Logic")
    print("="*50)
    
    demo = QuickDynamicDemo()
    
    # Test standup phase
    demo.context["phase"] = "standup"
    demo.context["consecutive_technical"] = 0
    
    demo.update_message_counters("technical_discussion")
    standup_intervention = demo.should_moderator_intervene("technical_discussion")
    
    print(f"📋 Standup phase:")
    print(f"   1st technical: counter={demo.context['consecutive_technical']}, intervene={standup_intervention}")
    
    # Test discussion phase
    demo.context["phase"] = "discussion"
    demo.context["consecutive_technical"] = 0
    
    demo.update_message_counters("technical_discussion")
    discussion_intervention_1 = demo.should_moderator_intervene("technical_discussion")
    
    demo.update_message_counters("technical")
    discussion_intervention_2 = demo.should_moderator_intervene("technical")
    
    demo.update_message_counters("question")
    discussion_intervention_3 = demo.should_moderator_intervene("question")
    
    print(f"💬 Discussion phase:")
    print(f"   1st technical: counter=1, intervene={discussion_intervention_1}")
    print(f"   2nd technical: counter=2, intervene={discussion_intervention_2}")
    print(f"   3rd technical: counter=3, intervene={discussion_intervention_3}")
    
    print(f"\n✅ Results:")
    print(f"   Standup 1st technical: {standup_intervention} (expected: True)")
    print(f"   Discussion 1st technical: {discussion_intervention_1} (expected: False)")
    print(f"   Discussion 2nd technical: {discussion_intervention_2} (expected: False)")
    print(f"   Discussion 3rd technical: {discussion_intervention_3} (expected: True)")

def main():
    print("🔧 Technical Discussion Interruption Tests")
    print("="*70)
    
    test_technical_discussion_interruption()
    test_mixed_message_types()
    test_standup_vs_discussion_phases()
    
    print("\n✅ All technical discussion tests completed!")

if __name__ == "__main__":
    main()
