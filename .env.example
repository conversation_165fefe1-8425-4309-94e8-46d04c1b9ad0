# =============================================================================
# Daily Meeting Emulator - Environment Configuration
# =============================================================================

# Google Gemini API Key (REQUIRED)
# Get your free API key at: https://makersuite.google.com/app/apikey
# Used for: AI dialogue generation, conversation context, personality responses
GEMINI_API_KEY=your_gemini_api_key_here

# Deepgram API Key (OPTIONAL - for voice generation)
# Get your free API key at: https://console.deepgram.com/
# Used for: Text-to-speech synthesis, voice personality mapping, audio export
# Features enabled: Real-time voice playback, WAV file generation, SSML enhancements
DEEPGRAM_API_KEY=your_deepgram_api_key_here

# =============================================================================
# Optional Configuration
# =============================================================================

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Default output directory for generated files
OUTPUT_DIR=data/generated

# Default audio output directory (when voice is enabled)
AUDIO_DIR=data/audio

# Default configuration file
DEFAULT_CONFIG=config/default_meeting.yaml

# =============================================================================
# Usage Examples:
# =============================================================================
#
# Text-only generation:
#   python main.py generate --participants 3 --duration 5
#
# With voice synthesis:
#   python main.py generate --participants 2 --duration 3 --voice
#
# Fast generation (no timing delays):
#   python main.py generate --participants 4 --duration 2 --fast
#
# Custom configuration:
#   python main.py generate --config custom_config.yaml --voice
# =============================================================================
