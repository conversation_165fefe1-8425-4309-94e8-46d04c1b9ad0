#!/usr/bin/env python3
"""
Test Jira Ticket Detection

Tests the Jira ticket suggestion detection logic.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from quick_dynamic_demo import QuickDynamicDemo

async def test_jira_detection():
    """Test AI-powered Jira ticket suggestion detection"""
    print("🧪 Testing AI Jira Ticket Detection")
    print("="*50)

    demo = QuickDynamicDemo()

    # Initialize Gemini client for testing
    if not await demo.initialize():
        print("❌ Failed to initialize AI client - skipping AI tests")
        return

    test_messages = [
        # Should be detected as ticket suggestions
        "We should create a Jira ticket for this performance issue",
        "Let's create a ticket to track the API optimization",
        "Should we file a ticket for the database refactoring?",
        "I think we need to create an issue for this bug",
        "We should track this frontend component work",
        "Can someone file this for later investigation?",
        "Let's add this to our backlog",
        "We need to document this issue for follow-up",

        # Should NOT be detected as ticket suggestions
        "This is just a regular technical discussion",
        "I'm working on the React components today",
        "The database queries need optimization",
        "What do you think about this approach?",
        "I finished the API implementation yesterday",
        "The performance looks good now",
        "How should we handle this edge case?",
    ]

    for message in test_messages:
        print(f"🔍 Testing: '{message}'")
        result = await demo.detect_jira_ticket_suggestion(message)

        if result["detected"]:
            print(f"✅ DETECTED (confidence: {result.get('confidence', 0):.2f})")
            print(f"   📋 Title: {result['title']}")
            print(f"   👤 Suggested assignee: {result['suggested_assignee']}")
            print(f"   🔧 Work type: {result.get('work_type', 'N/A')}")
            print(f"   💭 Reasoning: {result.get('reasoning', 'N/A')}")
        else:
            print(f"❌ Not detected")
        print()

def test_assignee_suggestion():
    """Test assignee suggestion logic"""
    print("🧪 Testing Assignee Suggestion Logic")
    print("="*50)
    
    demo = QuickDynamicDemo()
    
    test_cases = [
        ("We need to fix the React component performance", "frontend"),
        ("The API endpoints need refactoring", "backend"),
        ("Database optimization is required", "backend"),
        ("UI components need updating", "frontend"),
        ("Deployment pipeline needs improvement", "devops"),
        ("Alice should handle the frontend work", "Alice"),
        ("Bob can work on the backend API", "Bob"),
        ("General development task", "random"),
    ]
    
    for message, expected_type in test_cases:
        assignee = demo.suggest_ticket_assignee(message)
        print(f"📝 '{message[:40]}...' → {assignee}")
        
        if expected_type == "frontend":
            # Should assign to frontend developer
            participant = demo.participants.get(assignee, {})
            is_correct = participant.get("expertise") == "frontend"
        elif expected_type == "backend":
            # Should assign to backend developer
            participant = demo.participants.get(assignee, {})
            is_correct = participant.get("expertise") == "backend"
        elif expected_type == "devops":
            # Should assign to devops engineer
            participant = demo.participants.get(assignee, {})
            is_correct = participant.get("expertise") == "devops"
        elif expected_type in demo.participants:
            # Should assign to specific person
            is_correct = assignee == expected_type
        else:
            # Random assignment is always correct
            is_correct = True
        
        status = "✅" if is_correct else "❌"
        print(f"   {status} Expected: {expected_type}, Got: {assignee}")
        print()

def test_title_extraction():
    """Test ticket title extraction"""
    print("🧪 Testing Ticket Title Extraction")
    print("="*50)
    
    demo = QuickDynamicDemo()
    
    test_messages = [
        "We have a performance issue with the dashboard",
        "There's a bug in the authentication system",
        "We need to refactor the payment processing code",
        "Let's add tests for the new API endpoints",
        "The database queries are too slow",
        "Frontend components need updating",
        "Backend services need optimization",
        "General development work needed",
    ]
    
    for message in test_messages:
        title = demo.extract_ticket_title(message)
        print(f"📝 '{message}' → '{title}'")

async def main():
    print("🎫 Jira Ticket Detection Tests")
    print("="*60)

    await test_jira_detection()
    test_assignee_suggestion()
    test_title_extraction()

    print("✅ All Jira detection tests completed!")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
