"""
Gemini Engine for AI-powered Dialogue Generation

Integrates with Google Gemini 2.5 Flash to generate realistic
meeting dialogue based on participant personalities and context.
"""

import os
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import google.generativeai as genai

logger = logging.getLogger(__name__)


@dataclass
class GenerationConfig:
    """Configuration for Gemini content generation"""
    temperature: float = 1.2  # Increased creativity for less repetition (0.0-2.0)
    max_output_tokens: int = 500  # Maximum response length
    top_p: float = 0.95  # Increased nucleus sampling for more diversity
    top_k: int = 50  # Increased top-k for more variety


class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, requests_per_minute: int = 60, requests_per_day: int = 1500):
        self.requests_per_minute = requests_per_minute
        self.requests_per_day = requests_per_day
        self.calls = []
        logger.info(f"Rate limiter: {requests_per_minute} req/min, {requests_per_day} req/day")
    
    def wait_if_needed(self):
        """Wait if rate limit would be exceeded"""
        now = time.time()
        
        # Remove calls older than 1 minute
        self.calls = [call_time for call_time in self.calls if now - call_time < 60]
        
        # Check if we need to wait
        if len(self.calls) >= self.requests_per_minute:
            sleep_time = 60 - (now - self.calls[0])
            if sleep_time > 0:
                logger.info(f"Rate limit reached, waiting {sleep_time:.1f} seconds")
                time.sleep(sleep_time)
        
        # Record this call
        self.calls.append(now)


class GeminiEngine:
    """
    Google Gemini API integration for dialogue generation
    
    Handles API communication, prompt engineering, and response processing
    for generating realistic meeting dialogue.
    """
    
    def __init__(self, api_key: str, config: Optional[GenerationConfig] = None, model_name: Optional[str] = None):
        """
        Initialize Gemini engine

        Args:
            api_key: Google Gemini API key
            config: Generation configuration
            model_name: Specific model to use (if None, will auto-select best)
        """
        self.api_key = api_key
        self.config = config or GenerationConfig()

        # Configure Gemini
        genai.configure(api_key=api_key)

        # Use stable model with good quota limits
        if model_name is None:
            model_name = "gemini-1.5-flash"  # Stable model: 1000 req/min, well-tested

        self.model_name = model_name

        # Initialize model
        self.model = genai.GenerativeModel(
            self.model_name,
            generation_config=genai.types.GenerationConfig(
                temperature=self.config.temperature,
                max_output_tokens=self.config.max_output_tokens,
                top_p=self.config.top_p,
                top_k=self.config.top_k,
            )
        )

        # Set up rate limiting based on model (90% of actual limit for safety)
        rate_limits = self._get_rate_limits(self.model_name)
        safe_rpm = int(rate_limits["rpm"] * 0.9)  # 90% of limit for safety
        self.rate_limiter = RateLimiter(
            requests_per_minute=safe_rpm,
            requests_per_day=rate_limits["rpd"]
        )

        # Response cache to avoid duplicate API calls
        self.response_cache: Dict[str, str] = {}

        logger.info(f"Gemini engine initialized with model: {self.model_name}")

    def _select_best_model(self) -> str:
        """Select the best available model based on quotas"""
        try:
            from utils.quota_checker import check_quotas_and_select_model

            print("🔍 Checking available Gemini models and quotas...")
            best_model, quotas = check_quotas_and_select_model(self.api_key)

            if best_model:
                print(f"✅ Selected model: {best_model}")
                return best_model
            else:
                print("⚠️  No models available, falling back to default")
                return "gemini-1.5-flash"

        except Exception as e:
            logger.warning(f"Failed to check quotas: {e}")
            print(f"⚠️  Quota check failed, using default model: gemini-1.5-flash")
            return "gemini-1.5-flash"

    def _get_rate_limits(self, model_name: str) -> Dict[str, int]:
        """Get rate limits for a specific model based on quota analysis"""
        rate_limits = {
            # Best models (2000 req/min)
            "gemini-1.5-flash-8b": {"rpm": 2000, "rpd": 50000},
            "gemini-1.5-flash-8b-latest": {"rpm": 2000, "rpd": 50000},
            "gemini-1.5-flash-8b-001": {"rpm": 2000, "rpd": 50000},

            # Excellent models (1000 req/min)
            "gemini-1.5-flash": {"rpm": 1000, "rpd": 50000},
            "gemini-1.5-flash-latest": {"rpm": 1000, "rpd": 50000},
            "gemini-1.5-flash-002": {"rpm": 1000, "rpd": 50000},
            "gemini-2.0-flash-exp": {"rpm": 1000, "rpd": 50000},
            "gemini-2.0-flash": {"rpm": 1000, "rpd": 50000},
            "gemini-2.0-flash-001": {"rpm": 1000, "rpd": 50000},
            "gemini-2.0-flash-lite": {"rpm": 1000, "rpd": 50000},
            "gemini-2.0-flash-lite-001": {"rpm": 1000, "rpd": 50000},
            "gemini-2.5-flash-preview-05-20": {"rpm": 1000, "rpd": 50000},
            "gemini-2.5-flash-preview-04-17": {"rpm": 1000, "rpd": 50000},

            # Pro models (360 req/min)
            "gemini-1.5-pro": {"rpm": 360, "rpd": 50000},
            "gemini-1.5-pro-latest": {"rpm": 360, "rpd": 50000},
            "gemini-2.0-pro-exp": {"rpm": 360, "rpd": 50000},

            # Thinking models (500 req/min)
            "gemini-2.0-flash-thinking-exp-1219": {"rpm": 500, "rpd": 25000},

            # Legacy models (60 req/min)
            "gemini-pro": {"rpm": 60, "rpd": 1500},
            "gemini-pro-vision": {"rpm": 60, "rpd": 1500}
        }

        return rate_limits.get(model_name, {"rpm": 60, "rpd": 1500})

    def generate_dialogue(self,
                         participant_name: str,
                         participant_role: str,
                         personality_type: str,
                         personality_traits: Dict[str, float],
                         meeting_context: str,
                         meeting_phase: str,
                         response_type: str = "update") -> str:
        """
        Generate dialogue for a specific participant
        
        Args:
            participant_name: Name of the participant
            participant_role: Their role (scrum_master, senior_developer, etc.)
            personality_type: Personality archetype (rambler, questioner, etc.)
            personality_traits: Specific trait values
            meeting_context: Current meeting context and history
            meeting_phase: Current phase (opening, updates, blockers, closing)
            response_type: Type of response needed (update, question, interruption, etc.)
            
        Returns:
            Generated dialogue text
        """
        # Create cache key
        cache_key = self._create_cache_key(
            participant_name, personality_type, meeting_context[-200:], response_type
        )
        
        # Check cache first
        if cache_key in self.response_cache:
            logger.debug(f"Using cached response for {participant_name}")
            return self.response_cache[cache_key]
        
        # Build prompt
        prompt = self._build_prompt(
            participant_name, participant_role, personality_type,
            personality_traits, meeting_context, meeting_phase, response_type
        )
        
        # Generate response
        response = self._generate_with_retry(prompt)
        
        # Post-process response
        processed_response = self._post_process_response(
            response, personality_type, response_type
        )
        
        # Cache response
        self.response_cache[cache_key] = processed_response
        
        return processed_response
    
    def _build_prompt(self, 
                     participant_name: str,
                     participant_role: str,
                     personality_type: str,
                     personality_traits: Dict[str, float],
                     meeting_context: str,
                     meeting_phase: str,
                     response_type: str) -> str:
        """Build context-aware prompt for Gemini"""
        
        # Base prompt template
        base_prompt = f"""You are simulating a daily standup meeting participant with the following characteristics:

NAME: {participant_name}
ROLE: {participant_role}
PERSONALITY: {personality_type}

PERSONALITY TRAITS:
- Verbosity: {personality_traits.get('verbosity', 0.5):.1f} (0.0=brief, 1.0=verbose)
- Technical Detail: {personality_traits.get('technical_detail', 0.5):.1f} (0.0=high-level, 1.0=very detailed)
- Off-topic Tendency: {personality_traits.get('off_topic_probability', 0.3):.1f} (0.0=focused, 1.0=often off-topic)
- Curiosity: {personality_traits.get('curiosity', 0.5):.1f} (0.0=passive, 1.0=asks many questions)

MEETING CONTEXT:
{meeting_context}

CURRENT PHASE: {meeting_phase}
RESPONSE TYPE: {response_type}

"""
        
        # Add personality-specific instructions
        personality_instructions = self._get_personality_instructions(personality_type)
        prompt = base_prompt + personality_instructions
        
        # Add response type specific instructions
        response_instructions = self._get_response_type_instructions(response_type, meeting_phase, meeting_context)
        prompt += response_instructions
        
        # Add output format instructions
        prompt += """
OUTPUT REQUIREMENTS:
- Generate ONLY the participant's spoken words
- Do NOT include speaker name or timestamps
- Keep response natural and conversational
- Match the personality traits and role
- Consider the meeting context and phase
- Response should be 1-3 sentences unless personality requires more
- AVOID starting with "Okay", "So", "Well", "Alright" - be more direct
- Focus on STATEMENTS rather than questions in standup updates
- Use varied sentence starters for natural flow

RESPONSE:"""
        
        return prompt
    
    def _get_personality_instructions(self, personality_type: str) -> str:
        """Get personality-specific instructions"""
        
        instructions = {
            "facilitator": """
PERSONALITY BEHAVIOR:
- Keeps meetings on track and focused
- Manages time and ensures everyone participates
- Asks clarifying questions to move discussion forward
- Redirects off-topic conversations
- Summarizes and provides clear next steps
- Uses phrases like "Let's move on to..." or "To keep us on track..."
""",
            "rambler": """
PERSONALITY BEHAVIOR:
- Tends to provide excessive technical detail
- Often goes into implementation specifics
- May mention debugging stories or technical challenges
- Sometimes loses track of time while explaining
- Uses technical jargon and specific examples
""",
            "questioner": """
PERSONALITY BEHAVIOR:
- Frequently asks clarifying questions
- Seeks to understand unfamiliar concepts
- May interrupt with "What do you mean by...?"
- Shows curiosity about technical decisions
- Often asks follow-up questions
""",
            "minimalist": """
PERSONALITY BEHAVIOR:
- Gives brief, focused updates
- Avoids unnecessary detail
- Values efficiency and time
- Speaks in short, clear sentences
- Rarely elaborates unless asked
""",
            "problem_solver": """
PERSONALITY BEHAVIOR:
- Tries to solve problems during standup
- Offers solutions and suggestions
- May start architectural discussions
- Asks "How should we..." or "What if we..."
- Focuses on finding solutions
""",
            "storyteller": """
PERSONALITY BEHAVIOR:
- Provides context and background
- Explains the "why" behind decisions
- May share related experiences
- Uses narrative style in updates
- Connects current work to bigger picture
""",
            "interrupter": """
PERSONALITY BEHAVIOR:
- Frequently interrupts with related thoughts
- Adds comments during others' updates
- May change topics mid-conversation
- Shows high engagement but poor timing
- Often says "Oh, that reminds me..."
"""
        }
        
        return instructions.get(personality_type, "")
    
    def _get_response_type_instructions(self, response_type: str, meeting_phase: str, meeting_context: str = "") -> str:
        """Get response type specific instructions"""
        
        if response_type == "update" and meeting_phase == "updates":
            return """
CREATIVE STANDUP UPDATE GUIDELINES:

CORE STRUCTURE (be flexible with this):
- Yesterday's work: What you actually accomplished (be specific about technologies, features, systems)
- Today's focus: What you're planning to work on (concrete next steps)
- Blockers/needs: Anything slowing you down or help you need

CREATIVITY PRINCIPLES:
1. BE AUTHENTIC: Talk about your real work experience, not generic tasks
2. USE TECHNICAL SPECIFICITY: Mention actual technologies, frameworks, APIs, databases you work with
3. VARY YOUR LANGUAGE: Don't start every sentence the same way
4. SHOW PERSONALITY: Let your role and expertise shine through
5. BE CONVERSATIONAL: Talk like you're sharing news with colleagues, not reading a script

ROLE-BASED AUTHENTICITY:
- Developers: Talk about code, algorithms, performance, debugging, architecture decisions
- QA Engineers: Discuss testing strategies, bug discoveries, automation, quality metrics
- DevOps: Focus on infrastructure, deployments, monitoring, system reliability
- Designers: Mention user experience, prototypes, design systems, user research
- Product Managers: Discuss features, user feedback, roadmap decisions, stakeholder communication

LANGUAGE VARIETY (avoid repetitive patterns):
- Start with action words: "Completed", "Implemented", "Discovered", "Refactored", "Deployed"
- Use different time references: "Yesterday", "Last night", "This morning", "Earlier"
- Vary your focus: "My main achievement", "The breakthrough moment", "What took most of my time"

AVOID THESE REPETITIVE PATTERNS:
- Don't always start with "Yesterday I..."
- Don't use the same technical terms repeatedly
- Don't follow the exact same structure every time
- Don't sound robotic or templated

MAKE IT INTERESTING:
- Mention specific challenges you solved
- Share brief insights about technologies you're using
- Reference collaboration with teammates
- Include metrics or results when relevant
- Show enthusiasm for your work

REMEMBER: This is YOUR unique update reflecting YOUR actual work and personality.
"""
        

        
        elif response_type == "interruption":
            return """
INTERRUPTION RESPONSE:
Generate a brief interruption or interjection based on what the current speaker said.
Should feel natural and personality-appropriate.
"""
        
        elif response_type == "opening" and meeting_phase == "opening":
            return """
MEETING OPENING:
Generate an opening statement to start the daily standup meeting.
Should be appropriate for your role (especially if you're the Scrum Master).
"""
        
        elif response_type == "closing" and meeting_phase == "closing":
            return """
MEETING CLOSING:
Generate a closing statement to wrap up the daily standup.
Should be brief and appropriate for your role.
"""

        elif response_type == "question":
            if "response to this question" in meeting_context:
                return """
QUESTION RESPONSE FORMAT:
Generate a helpful response to the question that was asked.
Be specific and provide useful information.
Keep it conversational and relevant to the standup context.
"""
            else:
                return """
QUESTION FORMAT:
Generate a relevant question for a standup meeting.
If asking someone specific, address them by name: "Sarah, can you..." or "Mike, what do you think about..."
Questions should be brief and focused on work progress, blockers, or clarifications.
Examples:
- "Alex, do you know when the API documentation will be ready?"
- "Sarah, can you help me understand the new authentication flow?"
- "Mike, what's the timeline for the frontend changes?"
"""

        else:
            return """
GENERAL RESPONSE:
Generate an appropriate response based on the context and your personality.
"""
    
    def _generate_with_retry(self, prompt: str, max_retries: int = 3) -> str:
        """Generate response with retry logic and better error handling"""

        for attempt in range(max_retries):
            try:
                # Rate limiting
                self.rate_limiter.wait_if_needed()

                # Generate content with safety settings
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=self.config.temperature,
                        max_output_tokens=self.config.max_output_tokens,
                        top_p=self.config.top_p,
                        top_k=self.config.top_k,
                    ),
                    safety_settings=[
                        {
                            "category": "HARM_CATEGORY_HARASSMENT",
                            "threshold": "BLOCK_NONE"
                        },
                        {
                            "category": "HARM_CATEGORY_HATE_SPEECH",
                            "threshold": "BLOCK_NONE"
                        },
                        {
                            "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            "threshold": "BLOCK_NONE"
                        },
                        {
                            "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                            "threshold": "BLOCK_NONE"
                        }
                    ]
                )

                # Extract text with better error handling
                extracted_text = self._extract_response_text(response)
                if extracted_text:
                    return extracted_text.strip()
                else:
                    logger.warning(f"Could not extract text from response (attempt {attempt + 1})")

            except Exception as e:
                logger.error(f"Gemini API error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    raise

        raise Exception("Failed to generate response after all retries")

    def _extract_response_text(self, response) -> str:
        """Extract text from Gemini response with robust error handling"""
        try:
            # Try the standard way first
            if hasattr(response, 'text') and response.text:
                return response.text

            # If that fails, try to extract from candidates
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]

                # Check finish reason
                if hasattr(candidate, 'finish_reason'):
                    finish_reason = candidate.finish_reason
                    if finish_reason == 2:  # SAFETY
                        logger.warning("Response blocked by safety filters - using fallback")
                        print("⚠️  Content blocked by safety filters, using fallback response")
                        return self._get_safe_fallback_response()
                    elif finish_reason == 3:  # RECITATION
                        logger.warning("Response blocked due to recitation - using fallback")
                        print("⚠️  Content blocked due to recitation, using fallback response")
                        return self._get_safe_fallback_response()
                    elif finish_reason == 4:  # OTHER
                        logger.warning("Response blocked for other reasons - using fallback")
                        print("⚠️  Content blocked for other reasons, using fallback response")
                        return self._get_safe_fallback_response()

                # Try to extract content
                if hasattr(candidate, 'content') and candidate.content:
                    content = candidate.content
                    if hasattr(content, 'parts') and content.parts:
                        for part in content.parts:
                            if hasattr(part, 'text') and part.text:
                                return part.text

            # If all else fails, return None
            return None

        except Exception as e:
            logger.error(f"Error extracting response text: {e}")
            return None

    def _get_safe_fallback_response(self) -> str:
        """Get a safe fallback response when content is blocked"""
        fallbacks = [
            "Yesterday I worked on the user authentication module. Today I'm implementing the dashboard features.",
            "I completed the database migration yesterday. Today I'm focusing on API endpoint testing.",
            "Yesterday I fixed several bugs in the payment system. Today I'm working on the notification service.",
            "I finished the code review process yesterday. Today I'm implementing the search functionality.",
            "Yesterday I deployed the latest changes to staging. Today I'm working on performance optimizations.",
            "I completed the unit tests yesterday. Today I'm focusing on integration testing.",
            "Yesterday I worked on the mobile app features. Today I'm implementing the user profile section."
        ]
        import random
        return random.choice(fallbacks)
    
    def _post_process_response(self, 
                              response: str, 
                              personality_type: str, 
                              response_type: str) -> str:
        """Post-process the generated response"""
        
        # Clean up response
        response = response.strip()

        # Remove any speaker names or timestamps that might have been generated
        lines = response.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            # Skip empty lines
            if not line:
                continue
            # Skip lines that look like speaker names or timestamps
            if ':' in line and len(line.split(':')[0]) < 20:
                # Might be "Speaker 1:" or "John:" - skip if it looks like a speaker label
                parts = line.split(':', 1)
                if len(parts) == 2 and len(parts[0].strip()) < 20:
                    line = parts[1].strip()
            cleaned_lines.append(line)

        response = ' '.join(cleaned_lines)

        # Remove unnatural conversation starters and repetitive beginnings
        unnatural_starters = [
            'okay, so', 'ok, so', 'so,', 'well,', 'alright,', 'alright so',
            'okay', 'ok', 'well', 'so', 'um,', 'uh,', 'right,', 'right so',
            'regarding the', 'regarding', 'concerning the', 'concerning',
            'about the', 'as for the', 'as for'
        ]

        # Remove ANY repetitive topic starters - be very aggressive
        import re

        # Find patterns like "word word word," at the beginning
        repetitive_pattern = re.match(r'^([a-zA-Z\s]+),\s*', response)
        if repetitive_pattern:
            potential_topic = repetitive_pattern.group(1).lower().strip()

            # If it looks like a topic being repeated, remove it
            common_topics = [
                'api', 'deployment', 'database', 'validation', 'errors', 'integration',
                'caching', 'layer', 'performance', 'authentication', 'bug', 'interface',
                'rendering', 'pipeline', 'json', 'parsing', 'invalidation', 'testing',
                'migration', 'queries', 'procedures', 'functionality', 'regression'
            ]

            # If the potential topic contains common technical words, remove it
            if any(topic in potential_topic for topic in common_topics):
                response = response[len(repetitive_pattern.group(0)):].strip()
                if response:
                    response = response[0].upper() + response[1:]

        response_lower = response.lower()

        # Remove unnatural starters
        for starter in unnatural_starters:
            if response_lower.startswith(starter + ' '):
                response = response[len(starter):].strip()
                if response:
                    response = response[0].upper() + response[1:]
                response_lower = response.lower()
                break

        # Additional cleanup - remove any remaining comma-separated topic starters
        if ',' in response[:50]:  # Check first 50 characters
            parts = response.split(',', 1)
            if len(parts) == 2 and len(parts[0].strip()) < 30:  # Short topic phrase
                first_part = parts[0].strip().lower()
                # If first part looks like a topic, remove it
                if any(word in first_part for word in ['api', 'database', 'bug', 'error', 'issue']):
                    response = parts[1].strip()
                    if response:
                        response = response[0].upper() + response[1:]

        # Remove questions from updates - convert to statements
        if response_type == "update":
            # If response contains question marks, try to convert to statements
            if '?' in response:
                # Remove question marks and convert to statements
                response = response.replace('?', '.')

                # Convert common question patterns to statements
                question_patterns = [
                    ('Is anyone', 'I am'),
                    ('Are there', 'There are'),
                    ('Have you', 'I have'),
                    ('Did anyone', 'I'),
                    ('What are', 'The'),
                    ('How is', 'The'),
                    ('When will', 'I will'),
                ]

                for question, statement in question_patterns:
                    if response.lower().startswith(question.lower()):
                        response = response.replace(question, statement, 1)
                        break

        # Ensure response ends properly
        if response and not response.endswith(('.', '!', '?')):
            response += '.'

        return response
    
    def _create_cache_key(self,
                         participant_name: str,
                         personality_type: str,
                         context_snippet: str,
                         response_type: str) -> str:
        """Create cache key for response caching with timestamp to avoid repetition"""
        import hashlib
        import time

        # Add timestamp and response count to make keys more unique
        timestamp = int(time.time() * 1000)  # milliseconds for uniqueness
        response_count = len(self.response_cache)

        key_data = f"{participant_name}_{personality_type}_{response_type}_{context_snippet}_{timestamp}_{response_count}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def clear_cache(self):
        """Clear the response cache"""
        self.response_cache.clear()
        logger.info("Response cache cleared")
    
    def generate_participant_response(self,
                                    participant_name: str,
                                    personality_type: str,
                                    role: str,
                                    context: Dict[str, Any],
                                    prompt: str) -> str:
        """
        Generate participant response - compatibility method for MeetingSimulator

        Args:
            participant_name: Name of the participant
            personality_type: Personality archetype
            role: Participant role
            context: Meeting context dictionary
            prompt: Specific prompt for generation

        Returns:
            Generated response text
        """
        # Extract context information
        meeting_phase = context.get('meeting_phase', 'updates')
        meeting_context = f"Meeting phase: {meeting_phase}\n"

        if 'recent_statements' in context:
            meeting_context += "Recent statements:\n"
            for statement in context['recent_statements']:
                meeting_context += f"- {statement}\n"

        if 'active_topics' in context:
            meeting_context += f"Active topics: {', '.join(context['active_topics'])}\n"

        # Default personality traits
        personality_traits = {
            'verbosity': 0.5,
            'technical_detail': 0.5,
            'off_topic_probability': 0.3,
            'curiosity': 0.5
        }

        # Determine response type from prompt
        response_type = "update"
        if "opening" in prompt.lower():
            response_type = "opening"
        elif "closing" in prompt.lower():
            response_type = "closing"
        elif "question" in prompt.lower() or "response to this question" in prompt.lower():
            response_type = "question"
        elif "blocker" in prompt.lower():
            response_type = "blocker"

        # Generate dialogue using existing method
        return self.generate_dialogue(
            participant_name=participant_name,
            participant_role=role,
            personality_type=personality_type,
            personality_traits=personality_traits,
            meeting_context=meeting_context,
            meeting_phase=meeting_phase,
            response_type=response_type
        )

    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        return {
            "cache_size": len(self.response_cache),
            "cache_hits": getattr(self, '_cache_hits', 0),
            "cache_misses": getattr(self, '_cache_misses', 0)
        }
