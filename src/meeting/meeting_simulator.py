"""
Meeting Simulator

Core class for simulating daily standup meetings with AI-generated
dialogue based on participant personalities and meeting dynamics.
"""

import random
import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

# Rich console for beautiful output
from rich.console import Console
from rich.text import Text

# Import our modules
from output.transcript import MeetingTranscript, TranscriptSegment, ParticipantInfo
from dialogue.gemini_engine import GeminiEngine, GenerationConfig
from participants.base_participant import (
    BaseParticipant, PersonalityTraits, ConversationContext,
    Role, PersonalityType
)

# Voice integration (optional)
try:
    import sys
    from pathlib import Path

    # Add src directory to path if not already there
    src_path = Path(__file__).parent.parent
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))

    from voice.deepgram_agent import DeepgramVoiceAgent, create_voice_agent
    from voice.realtime_voice import RealTimeVoiceEngine, create_realtime_voice_engine
    from voice.meeting_audio import MeetingAudioGenerator, generate_meeting_audio_from_transcript
    from voice.simple_audio import SimpleAudioGenerator, generate_meeting_audio_simple
    from voice.basic_audio import BasicAudioGenerator, generate_meeting_audio_basic
    VOICE_AVAILABLE = True
except ImportError as e:
    VOICE_AVAILABLE = False
    DeepgramVoiceAgent = None
    create_voice_agent = None
    RealTimeVoiceEngine = None
    create_realtime_voice_engine = None
    MeetingAudioGenerator = None
    generate_meeting_audio_from_transcript = None
    SimpleAudioGenerator = None
    generate_meeting_audio_simple = None
    BasicAudioGenerator = None
    generate_meeting_audio_basic = None

    # Debug info
    print(f"Voice import failed: {e}")

logger = logging.getLogger(__name__)

# Initialize Rich console for beautiful output
console = Console()


class MeetingSimulator:
    """
    Simulates daily standup meetings with realistic participant interactions
    
    Uses AI-powered dialogue generation to create authentic meeting transcripts
    with configurable participant personalities and meeting dynamics.
    """
    
    def __init__(self, config: Dict[str, Any], api_key: str, real_time: bool = True, enable_voice: bool = False):
        """
        Initialize the meeting simulator

        Args:
            config: Meeting configuration dictionary
            api_key: Google Gemini API key
            real_time: Whether to simulate real speech timing (default: True)
            enable_voice: Whether to enable Deepgram voice generation (default: False)
        """
        self.config = config
        self.api_key = api_key
        self.real_time = real_time
        self.enable_voice = enable_voice and VOICE_AVAILABLE

        # Initialize components
        self._setup_gemini_engine()
        self._setup_participants()
        self._setup_meeting_parameters()

        # Initialize Voice Agent if enabled
        self.voice_agent = None
        self.realtime_voice = None
        if self.enable_voice:
            console.print("🎙️ [blue]Voice generation enabled - will create audio files[/blue]")
            # Initialize real-time voice engine
            try:
                self.realtime_voice = create_realtime_voice_engine()
                if self.realtime_voice:
                    console.print("🎤 [green]Real-time voice engine ready![/green]")
                else:
                    console.print("⚠️ [yellow]Falling back to file-based voice generation[/yellow]")
            except Exception as e:
                console.print(f"⚠️ [yellow]Real-time voice failed, using file mode: {e}[/yellow]")
        
        # Meeting state
        self.current_time = 0.0  # seconds
        self.current_phase = "opening"
        self.conversation_history: List[TranscriptSegment] = []
        self.active_topics: List[str] = []
        
        logger.info(f"Meeting simulator initialized with {len(self.participants)} participants")
    
    def simulate_meeting(self) -> MeetingTranscript:
        """
        Simulate a complete meeting and return transcript
        
        Returns:
            Complete meeting transcript with metadata
        """
        logger.info("Starting meeting simulation")
        
        # Initialize transcript
        transcript = MeetingTranscript()
        
        # Add participants to transcript
        for participant in self.participants:
            participant_info = ParticipantInfo(
                name=participant.name,
                role=participant.role.value,
                personality=participant.personality_type.value,
                traits=participant.personality_traits.__dict__
            )
            transcript.add_participant(participant_info)
        
        # Add meeting metadata
        transcript.metadata.update({
            'style': self.config['meeting']['style'],
            'duration_minutes': self.config['meeting']['duration_minutes'],
            'participant_count': len(self.participants),
            'off_topic_target_ratio': self.config['content']['off_topic_ratio'],
            'generated_at': datetime.now().isoformat(),
            'timestamp': datetime.now().strftime('%Y%m%d_%H%M%S')
        })
        
        # Simulate meeting phases
        try:
            self._simulate_opening_phase(transcript)
            self._simulate_updates_phase(transcript)
            self._simulate_blockers_phase(transcript)
            self._simulate_closing_phase(transcript)
            
            logger.info(f"Meeting simulation completed: {len(transcript.segments)} segments generated")

            # Generate complete meeting audio if voice is enabled
            if self.enable_voice:
                try:
                    console.print("\n🎵 [yellow]Generating meeting audio files...[/yellow]")

                    if VOICE_AVAILABLE:
                        # Use real audio generation
                        audio_files = generate_meeting_audio_basic(transcript)

                        if audio_files:
                            console.print(f"🎵 [green]Meeting audio files generated: {len(audio_files)} files[/green]")
                            console.print(f"📁 [blue]Audio files saved in: data/audio/[/blue]")
                        else:
                            console.print("❌ [red]Failed to generate meeting audio[/red]")
                    else:
                        # Demo mode - simulate audio generation
                        console.print("🎭 [yellow]Demo mode: Simulating audio generation...[/yellow]")

                        # Create demo audio file list
                        audio_dir = Path("data/audio")
                        audio_dir.mkdir(parents=True, exist_ok=True)

                        demo_files = []
                        meeting_id = transcript.metadata.get('timestamp', 'demo')

                        for i, segment in enumerate(transcript.segments):
                            participant = segment.participant.replace(" ", "_")
                            demo_filename = f"{meeting_id}_segment_{i:03d}_{participant}.wav"
                            demo_path = audio_dir / demo_filename

                            # Create empty demo file
                            demo_path.touch()
                            demo_files.append(str(demo_path))
                            console.print(f"   🎙️ [blue]Demo file: {demo_filename}[/blue]")

                        # Create demo playlist
                        playlist_path = audio_dir / f"{meeting_id}_playlist.txt"
                        with open(playlist_path, 'w') as f:
                            f.write(f"# Demo Meeting Audio Playlist: {meeting_id}\n")
                            f.write(f"# To enable real audio generation:\n")
                            f.write(f"# 1. Set DEEPGRAM_API_KEY in .env\n")
                            f.write(f"# 2. Install: pip install deepgram-sdk\n")
                            f.write(f"#\n")
                            f.write(f"# Features when enabled:\n")
                            f.write(f"# - Different voices for each personality type\n")
                            f.write(f"# - SSML enhanced speech with natural pauses\n")
                            f.write(f"# - Personality-specific speech patterns\n")
                            f.write(f"# - Emphasis on important meeting keywords\n")
                            f.write(f"# - Natural breathing patterns for long texts\n")
                            f.write(f"#\n\n")
                            for i, demo_file in enumerate(demo_files):
                                f.write(f"{i+1:2d}. {Path(demo_file).name}\n")

                        console.print(f"🎵 [green]Demo audio files created: {len(demo_files)} files[/green]")
                        console.print(f"📁 [blue]Demo files saved in: data/audio/[/blue]")
                        console.print(f"📋 [green]Demo playlist: {playlist_path}[/green]")
                        console.print("💡 [yellow]To enable real audio: set DEEPGRAM_API_KEY and install deepgram-sdk[/yellow]")

                except Exception as e:
                    logger.warning(f"Audio generation error: {e}")
                    console.print(f"⚠️ [yellow]Audio generation failed: {e}[/yellow]")
            else:
                console.print("ℹ️ [blue]Voice generation not enabled (use --voice flag)[/blue]")

        except Exception as e:
            logger.error(f"Error during meeting simulation: {e}")
            raise
        finally:
            # Clean up voice engine
            if self.realtime_voice:
                try:
                    self.realtime_voice.wait_for_completion()
                    self.realtime_voice.stop()
                except Exception as e:
                    logger.warning(f"Voice cleanup error: {e}")

        return transcript
    
    def _setup_gemini_engine(self):
        """Initialize the Gemini AI engine"""
        gemini_config = self.config.get('dialogue', {}).get('gemini', {})
        
        generation_config = GenerationConfig(
            temperature=gemini_config.get('temperature', 1.2),  # Higher for creativity
            max_output_tokens=gemini_config.get('max_tokens', 500),
            top_p=gemini_config.get('top_p', 0.95),  # More diversity
            top_k=gemini_config.get('top_k', 50)  # More variety
        )
        
        self.gemini_engine = GeminiEngine(self.api_key, generation_config)
    
    def _setup_participants(self):
        """Initialize meeting participants from configuration"""
        self.participants: List[BaseParticipant] = []
        
        participant_configs = self.config.get('participants', [])
        target_count = self.config['meeting']['participant_count']
        
        # Use configured participants or generate them
        if len(participant_configs) >= target_count:
            selected_configs = participant_configs[:target_count]
        else:
            # Extend with generated participants if needed
            selected_configs = participant_configs[:]
            while len(selected_configs) < target_count:
                selected_configs.append(self._generate_participant_config(len(selected_configs)))
        
        # Create participant instances
        for participant_config in selected_configs:
            participant = self._create_participant_from_config(participant_config)
            self.participants.append(participant)
    
    def _setup_meeting_parameters(self):
        """Setup meeting timing and phase parameters"""
        self.total_duration = self.config['meeting']['duration_minutes'] * 60  # Convert to seconds
        
        phases = self.config['meeting'].get('phases', {})
        self.phase_durations = {
            'opening': phases.get('opening_duration', 0.1) * self.total_duration,
            'updates': phases.get('updates_duration', 0.7) * self.total_duration,
            'blockers': phases.get('blockers_duration', 0.15) * self.total_duration,
            'closing': phases.get('closing_duration', 0.05) * self.total_duration
        }
    
    def _simulate_opening_phase(self, transcript: MeetingTranscript):
        """Simulate meeting opening phase with real-time streaming"""
        self.current_phase = "opening"
        phase_end_time = self.current_time + self.phase_durations['opening']

        console.print(f"\n🚀 [green]Starting Meeting - Opening Phase[/green]")

        # Scrum master opens the meeting
        scrum_master = self._find_scrum_master()
        if scrum_master:
            console.print(f"👋 [yellow]{scrum_master.name} is opening the meeting...[/yellow]")
            opening_statement = self._generate_contextual_response(scrum_master, "opening", transcript)
            self._add_segment(transcript, scrum_master, opening_statement)

        # Brief introductions or small talk - ensure at least one response
        interaction_count = 0
        max_interactions = min(3, len(self.participants) - 1)  # Allow more interactions

        while interaction_count < max_interactions and self.current_time < phase_end_time:
            participant = self._select_next_speaker()
            if participant and participant != scrum_master:
                console.print(f"💬 [cyan]{participant.name} is responding...[/cyan]")
                response = self._generate_contextual_response(participant, "opening_response", transcript)
                self._add_segment(transcript, participant, response)
                interaction_count += 1

                # Handle any questions that arise during opening
                self._handle_real_time_interactions(transcript, participant, phase_end_time)

                # Don't let opening phase take too long
                if self.current_time >= phase_end_time:
                    break

        # Before ending opening phase, check for any unanswered questions
        self._handle_pending_questions(transcript)
    
    def _simulate_updates_phase(self, transcript: MeetingTranscript):
        """Simulate main updates phase with real-time streaming"""
        self.current_phase = "updates"
        phase_end_time = self.current_time + self.phase_durations['updates']

        console.print(f"\n🔄 [cyan]Starting Updates Phase[/cyan]")

        # Each participant gives their update
        participants_copy = self.participants.copy()
        random.shuffle(participants_copy)  # Randomize order

        for participant in participants_copy:
            if self.current_time >= phase_end_time:
                break

            console.print(f"\n👤 [yellow]{participant.name} is giving their update...[/yellow]")

            # Main update with full conversation context
            update = self._generate_contextual_response(participant, "update", transcript)

            # For updates, we need to manually handle the display since we're not using _generate_with_gemini
            if update:
                # Calculate speech duration for real-time simulation
                from utils.speech_timing import calculate_speech_duration
                import time

                speech_duration = calculate_speech_duration(
                    text=update,
                    personality_type=participant.personality_type.value,
                    role=participant.role.value
                )

                # Print to console for user feedback using Rich
                console.print(f"🎭 [bold]{participant.name}[/bold] ([italic]{participant.personality_type.value}[/italic]):")
                console.print(f"   💬 {update}")

                # REAL-TIME VOICE: Speak immediately if enabled
                if self.enable_voice and self.realtime_voice:
                    try:
                        # Speak immediately in real-time
                        self.realtime_voice.speak_immediately(
                            text=update,
                            participant_name=participant.name,
                            personality_type=participant.personality_type.value,
                            block=False  # Don't block - continue with meeting
                        )
                    except Exception as e:
                        logger.warning(f"Real-time voice failed: {e}")

                # REAL-TIME SIMULATION: Wait for the actual speech duration (if enabled)
                if self.real_time:
                    console.print(f"   🔊 Speaking... (waiting {speech_duration:.1f}s)")
                    time.sleep(speech_duration)
                    console.print(f"   ✅ Finished speaking")

                console.print()  # Empty line for readability

            self._add_segment(transcript, participant, update)

            # Real-time follow-up interactions
            self._handle_real_time_interactions(transcript, participant, phase_end_time)

    def _generate_contextual_response(self, participant: BaseParticipant, response_type: str, transcript: MeetingTranscript) -> str:
        """Generate response with full conversation context"""

        # Build comprehensive context from entire conversation
        conversation_context = self._build_full_conversation_context(transcript)

        # Create context-aware prompt
        if response_type == "update":
            # Generate completely independent updates without any context
            work_items = [
                "authentication system", "user dashboard", "payment processing",
                "search functionality", "notification service", "data migration",
                "API endpoints", "database optimization", "security patches",
                "mobile app features", "reporting system", "backup procedures"
            ]

            import random
            work_item = random.choice(work_items)

            # Use predefined templates to avoid LLM confusion
            templates = [
                f"Yesterday I fixed the authentication bug in the {work_item}. Today I'm implementing security improvements for user sessions.",
                f"Yesterday I completed the code review for the {work_item}. Today I'm working on performance optimizations.",
                f"Yesterday I deployed updates to the {work_item}. Today I'm monitoring the system and fixing any issues.",
                f"Yesterday I tested the new features in the {work_item}. Today I'm writing documentation and preparing for release.",
                f"Yesterday I refactored the database queries for the {work_item}. Today I'm implementing caching to improve response times.",
                f"Yesterday I resolved the memory leak in the {work_item}. Today I'm adding monitoring to prevent similar issues.",
                f"Yesterday I integrated the API endpoints for the {work_item}. Today I'm working on error handling and validation."
            ]

            import random
            # Return the template directly - no LLM needed for updates
            selected_template = random.choice(templates)
            return selected_template

        elif response_type == "question":
            recent_speaker = transcript.segments[-1].participant if transcript.segments else "someone"

            prompt = f"""You are {participant.name}, a {participant.role.value}.

TASK: Ask {recent_speaker} a short, specific question about their work.

RULES:
- Start with "{recent_speaker}, ..."
- Ask about timeline, help needed, or approach
- Be direct and brief
- NO topic repetition

Examples:
- "{recent_speaker}, how long will that take?"
- "{recent_speaker}, need any help with that?"
- "{recent_speaker}, which approach are you using?"

Generate your question:"""

        elif response_type == "response":
            # Find the question being responded to
            question_segment = None
            for segment in reversed(transcript.segments):
                if self._contains_question(segment.content):
                    question_segment = segment
                    break

            question_text = question_segment.content if question_segment else "the previous question"
            questioner = question_segment.participant if question_segment else "someone"

            prompt = f"""You are {participant.name}, a {participant.role.value}.

TASK: Answer {questioner}'s question: "{question_text}"

RULES:
- Give a direct, helpful answer
- Be specific and concrete
- Keep it brief (1-2 sentences)
- NO topic repetition

Generate your answer:"""

        elif response_type == "opening":
            prompt = f"""You are {participant.name}, a {participant.role.value} with {participant.personality_type.value} personality.

CURRENT SITUATION:
- You are starting the daily standup meeting
- You need to open the meeting professionally
- Set the tone and agenda for the team

CONVERSATION CONTEXT:
{conversation_context}

Generate an opening statement to start the standup. Be professional but match your personality.
Examples: "Good morning everyone, let's start our daily standup..." or "Hi team, ready for today's updates?"

Generate only your spoken words:"""

        elif response_type == "opening_response":
            prompt = f"""You are {participant.name}, a {participant.role.value} with {participant.personality_type.value} personality.

CURRENT SITUATION:
- The meeting has just started
- Someone opened the meeting
- You want to acknowledge and show readiness
- This is NOT the time for technical questions or detailed work discussions
- Keep it simple and professional

CONVERSATION CONTEXT:
{conversation_context}

Generate a brief, professional response showing you're ready for the standup.
Examples: "Good morning", "Ready to start", "Morning everyone", "Let's do this", "Sounds good"
DO NOT ask technical questions or mention specific work items yet.
DO NOT start with "Okay", "So", "Well", "Regarding"
Keep it short and natural - maximum 1-2 sentences.

Generate only your spoken words:"""

        else:
            prompt = f"""You are {participant.name}, a {participant.role.value} with {participant.personality_type.value} personality.

CONVERSATION CONTEXT:
{conversation_context}

Generate an appropriate response for the current situation in this standup meeting.

Generate only your spoken words:"""

        return self._generate_with_gemini(participant, prompt)

    def _build_full_conversation_context(self, transcript: MeetingTranscript) -> str:
        """Build comprehensive conversation context with repetition prevention"""
        if not transcript.segments:
            return "Meeting just started."

        context_lines = []
        context_lines.append("CONVERSATION HISTORY:")

        # Include recent segments (last 5-8 for context)
        recent_segments = transcript.segments[-8:] if len(transcript.segments) > 8 else transcript.segments

        for segment in recent_segments:
            timestamp = f"{int(segment.timestamp//60):02d}:{int(segment.timestamp%60):02d}"

            # Clean the content to remove repetitive patterns before adding to context
            cleaned_content = self._clean_content_for_context(segment.content)
            context_lines.append(f"[{timestamp}] {segment.participant}: {cleaned_content}")

        # Add current meeting state
        context_lines.append(f"\nCURRENT STATE:")
        context_lines.append(f"- Phase: {self.current_phase}")
        context_lines.append(f"- Participants: {', '.join([p.name for p in self.participants])}")
        context_lines.append(f"- Time elapsed: {self.current_time/60:.1f} minutes")

        # Add anti-repetition guidance
        if recent_segments:
            # Extract common phrases from recent messages to avoid
            recent_text = " ".join([s.content for s in recent_segments[-3:]])

            context_lines.append(f"\n🚫 CRITICAL ANTI-REPETITION RULES:")
            context_lines.append(f"- NEVER start with topics mentioned by others: avoid copying phrases from recent messages")
            context_lines.append(f"- DO NOT begin with: 'API deployment', 'Data validation', 'Database issues', etc.")
            context_lines.append(f"- DO NOT copy sentence patterns from previous speakers")
            context_lines.append(f"- Start with YOUR OWN words: 'I think...', 'Yesterday I...', 'My concern is...', etc.")
            context_lines.append(f"- Be ORIGINAL and NATURAL - don't echo others")
            context_lines.append(f"- Focus on YOUR perspective, not repeating topics")

        return "\n".join(context_lines)

    def _clean_content_for_context(self, content: str) -> str:
        """Clean content to remove repetitive patterns before adding to context"""
        import re

        # Remove repetitive topic starters that cause copying
        # Pattern: "Topic words, rest of sentence"
        pattern = r'^([a-zA-Z\s]+),\s*(.+)$'
        match = re.match(pattern, content)

        if match:
            topic_part = match.group(1).lower().strip()
            rest_part = match.group(2).strip()

            # Common technical topics that get repeated
            repetitive_topics = [
                'api', 'deployment', 'endpoint', 'database', 'validation', 'errors',
                'integration', 'documentation', 'changes', 'update', 'status',
                'testing', 'procedures', 'impact', 'legacy', 'mobile', 'user'
            ]

            # If topic contains repetitive words, remove it
            if any(topic in topic_part for topic in repetitive_topics):
                # Return just the meaningful part
                return rest_part[0].upper() + rest_part[1:] if rest_part else content

        return content

    def _get_personality_guidance(self, personality_type: PersonalityType) -> str:
        """Get personality-specific guidance"""
        guidance = {
            PersonalityType.FACILITATOR: "Keep the meeting on track, ask clarifying questions, manage time",
            PersonalityType.RAMBLER: "Provide detailed technical explanations, go into implementation details",
            PersonalityType.QUESTIONER: "Ask follow-up questions, seek clarification, show curiosity",
            PersonalityType.MINIMALIST: "Be brief and to the point, avoid unnecessary details",
            PersonalityType.PROBLEM_SOLVER: "Offer solutions, suggest improvements, focus on solving issues",
            PersonalityType.STORYTELLER: "Provide context and background, explain the 'why' behind decisions",
            PersonalityType.INTERRUPTER: "Jump in with related thoughts, add comments to others' updates"
        }
        return guidance.get(personality_type, "Be natural and authentic")

    def _handle_real_time_interactions(self, transcript: MeetingTranscript, speaker: BaseParticipant, phase_end_time: float):
        """Handle real-time interactions after someone speaks"""
        if self.current_time >= phase_end_time:
            return

        # Reduced chance for questions or comments (standup should be mostly updates)
        interaction_chance = 0.2  # 20% chance of interaction (reduced from 40%)

        if random.random() < interaction_chance:
            # Someone might ask a question or make a comment
            potential_responders = [p for p in self.participants if p != speaker]

            if potential_responders:
                responder = random.choice(potential_responders)

                # Determine type of interaction
                last_statement = transcript.segments[-1].content if transcript.segments else ""

                if self._should_ask_question(responder, last_statement):
                    console.print(f"💬 [green]{responder.name} has a question...[/green]")
                    question = self._generate_contextual_response(responder, "question", transcript)
                    self._add_segment_without_question_handling(transcript, responder, question)

                    # The original speaker should almost always respond to direct questions
                    if random.random() < 0.9:  # 90% chance of response (increased from 70%)
                        console.print(f"💭 [blue]{speaker.name} is responding...[/blue]")
                        response = self._generate_contextual_response(speaker, "response", transcript)
                        self._add_segment_without_question_handling(transcript, speaker, response)

    def _should_ask_question(self, participant: BaseParticipant, last_statement: str) -> bool:
        """Determine if participant should ask a question (reduced for more natural standup)"""
        # Reduced base probability for more natural standup flow
        base_prob = 0.15  # Reduced from 0.3

        if participant.personality_type == PersonalityType.QUESTIONER:
            base_prob = 0.4  # Reduced from 0.7
        elif participant.personality_type == PersonalityType.MINIMALIST:
            base_prob = 0.05  # Reduced from 0.1
        elif participant.personality_type == PersonalityType.FACILITATOR:
            base_prob = 0.25  # Reduced from 0.5
        elif participant.personality_type == PersonalityType.RAMBLER:
            base_prob = 0.1  # Ramblers focus on talking, not asking
        elif participant.personality_type == PersonalityType.PROBLEM_SOLVER:
            base_prob = 0.2  # Moderate questioning

        # Slightly increase probability if technical terms mentioned
        technical_keywords = ['api', 'database', 'bug', 'error', 'issue', 'architecture', 'performance', 'blocker']
        if any(keyword in last_statement.lower() for keyword in technical_keywords):
            base_prob += 0.1  # Reduced from 0.2

        return random.random() < base_prob

    def _generate_voice_sync(self, text: str, participant: BaseParticipant, segment_id: int):
        """Generate voice audio for a segment synchronously"""
        if not self.enable_voice or not VOICE_AVAILABLE:
            return

        try:
            # Initialize voice agent if not already done
            if not self.voice_agent:
                self.voice_agent = DeepgramVoiceAgent()

            # Generate audio file
            filename = f"segment_{segment_id:03d}_{participant.name.replace(' ', '_')}.wav"

            # Run async function in new event loop
            import asyncio
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                audio_file = loop.run_until_complete(
                    self.voice_agent.text_to_speech(
                        text=text,
                        personality_type=participant.personality_type.value,
                        participant_name=participant.name,
                        filename=filename
                    )
                )
                loop.close()

                if audio_file:
                    console.print(f"🎵 [green]Audio generated: {filename}[/green]")

            except Exception as e:
                logger.warning(f"Voice generation async error: {e}")

        except Exception as e:
            logger.warning(f"Voice generation failed for {participant.name}: {e}")

    def _handle_pending_questions(self, transcript: MeetingTranscript):
        """Handle any unanswered questions before phase transition"""
        if not transcript.segments:
            return

        # Check if the last segment was a question that hasn't been answered
        last_segment = transcript.segments[-1]
        if self._contains_question(last_segment.content):
            # Extract who the question was addressed to
            addressee = self._extract_question_addressee(last_segment.content)
            if addressee:
                target_participant = self._find_participant_by_name(addressee)
                if target_participant:
                    console.print(f"💭 [blue]{target_participant.name} is answering the pending question...[/blue]")
                    response = self._generate_question_response(target_participant,
                                                             self._find_participant_by_name(last_segment.participant),
                                                             last_segment.content)
                    if response:
                        self._add_segment_without_question_handling(transcript, target_participant, response)

    def _simulate_blockers_phase(self, transcript: MeetingTranscript):
        """Simulate blockers discussion phase"""
        self.current_phase = "blockers"
        phase_end_time = self.current_time + self.phase_durations['blockers']
        
        # Some participants mention blockers
        participants_with_blockers = random.sample(
            self.participants, 
            k=min(2, len(self.participants))
        )
        
        for participant in participants_with_blockers:
            if self.current_time >= phase_end_time:
                break
                
            blocker = self._generate_blocker_statement(participant)
            self._add_segment(transcript, participant, blocker)
            
            # Possible solutions or discussions
            self._handle_blocker_discussion(transcript, participant)
    
    def _simulate_closing_phase(self, transcript: MeetingTranscript):
        """Simulate meeting closing phase"""
        self.current_phase = "closing"
        
        # Scrum master closes the meeting
        scrum_master = self._find_scrum_master()
        if scrum_master:
            closing_statement = self._generate_closing_statement(scrum_master)
            self._add_segment(transcript, scrum_master, closing_statement)
    
    def _find_scrum_master(self) -> Optional[BaseParticipant]:
        """Find the scrum master participant"""
        for participant in self.participants:
            if participant.role == Role.SCRUM_MASTER:
                return participant
        return self.participants[0] if self.participants else None
    
    def _select_next_speaker(self) -> Optional[BaseParticipant]:
        """Select the next participant to speak"""
        # Simple selection logic - can be enhanced
        available_participants = [p for p in self.participants if p.speaking_time_used < 2.0]
        if not available_participants:
            available_participants = self.participants
        
        return random.choice(available_participants) if available_participants else None
    
    def _generate_opening_statement(self, participant: BaseParticipant) -> str:
        """Generate opening statement for the meeting"""
        prompt = f"""Generate a brief opening statement for a daily standup meeting. 
        The speaker is {participant.name}, a {participant.role.value}.
        Keep it professional but {self.config['meeting']['style']}.
        Maximum 2 sentences."""
        
        return self._generate_with_gemini(participant, prompt)
    
    def _generate_participant_update(self, participant: BaseParticipant) -> str:
        """Generate a participant's daily update"""
        context = self._build_conversation_context()
        
        prompt = f"""Generate a daily standup update for {participant.name}, a {participant.role.value}.
        Personality: {participant.personality_type.value}
        
        Include:
        - What they worked on yesterday
        - What they plan to work on today
        - Any impediments (optional)
        
        Style: {self.config['meeting']['style']}
        Keep it realistic and role-appropriate."""
        
        response = self._generate_with_gemini(participant, prompt)

        # Determine if this should be off-topic based on configuration
        if self._should_be_off_topic(participant):
            response = self._make_response_off_topic(response, participant)

        return response
    
    def _generate_with_gemini(self, participant: BaseParticipant, prompt: str) -> str:
        """Generate response using Gemini AI - always use API, no fallbacks"""
        context = self._build_conversation_context()

        # Always use Gemini API - retry on failure
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = self.gemini_engine.generate_participant_response(
                    participant_name=participant.name,
                    personality_type=participant.personality_type.value,
                    role=participant.role.value,
                    context=context,
                    prompt=prompt
                )

                # Calculate speech duration for real-time simulation
                from utils.speech_timing import calculate_speech_duration
                import time

                speech_duration = calculate_speech_duration(
                    text=response,
                    personality_type=participant.personality_type.value,
                    role=participant.role.value
                )

                # Print to console for user feedback using Rich
                console.print(f"🎭 [bold]{participant.name}[/bold] ([italic]{participant.personality_type.value}[/italic]):")
                console.print(f"   💬 {response}")

                # REAL-TIME VOICE: Speak immediately if enabled
                if self.enable_voice and self.realtime_voice:
                    try:
                        self.realtime_voice.speak_immediately(
                            text=response,
                            participant_name=participant.name,
                            personality_type=participant.personality_type.value,
                            block=False
                        )
                    except Exception as e:
                        logger.warning(f"Real-time voice failed for {participant.name}: {e}")

                # REAL-TIME SIMULATION: Wait for the actual speech duration (if enabled)
                if self.real_time:
                    console.print(f"   🔊 Speaking... (waiting {speech_duration:.1f}s)")
                    time.sleep(speech_duration)
                    console.print(f"   ✅ Finished speaking")

                console.print()  # Empty line for readability

                return response

            except Exception as e:
                logger.warning(f"Gemini generation failed for {participant.name} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(1)  # Wait before retry
                else:
                    # If all retries failed, raise the exception
                    raise Exception(f"Failed to generate response for {participant.name} after {max_retries} attempts: {e}")
    
    def _should_be_off_topic(self, participant: BaseParticipant) -> bool:
        """Determine if participant's response should be off-topic"""
        target_ratio = self.config['content']['off_topic_ratio']
        current_ratio = len([s for s in self.conversation_history if s.is_off_topic]) / max(1, len(self.conversation_history))

        # Increase base probability for off-topic content
        base_probability = 0.4  # 40% base chance

        # If we're below target ratio, significantly increase chance
        if current_ratio < target_ratio:
            multiplier = 2.0  # Double the chance if we're below target
            final_probability = min(0.8, base_probability * multiplier)  # Cap at 80%
            return random.random() < final_probability

        # Even if we're at target, still allow some off-topic content
        return random.random() < (base_probability * 0.3)  # 12% chance even when at target
    
    def _make_response_off_topic(self, response: str, participant: BaseParticipant) -> str:
        """Modify response to be off-topic"""
        off_topic_additions = [
            # Personal/casual topics
            "Oh, and speaking of that, did anyone see the game last night?",
            "By the way, has anyone tried the new coffee machine?",
            "That reminds me, is anyone going to the team lunch on Friday?",
            "Quick question - did anyone catch the weather forecast for this weekend?",
            "Speaking of which, how was everyone's weekend?",

            # Work-adjacent but off-topic
            "Actually, I've been thinking about our office setup lately...",
            "That reminds me, we should really discuss our deployment process sometime...",
            "By the way, has anyone heard about the new company policy changes?",
            "Oh, and I meant to ask - how's the new parking situation working out?",

            # Technical but not standup-relevant
            "That makes me think about the architecture refactoring we discussed months ago...",
            "Speaking of issues, did anyone see that interesting article about microservices?",
            "This reminds me of a similar problem I had at my previous company...",
        ]

        addition = random.choice(off_topic_additions)
        return f"{response} {addition}"
    
    def _add_segment(self, transcript: MeetingTranscript, participant: BaseParticipant, content: str):
        """Add a new segment to the transcript"""
        # Calculate realistic speaking time based on content and personality
        from utils.speech_timing import calculate_speech_duration

        speaking_time = calculate_speech_duration(
            text=content,
            personality_type=participant.personality_type.value,
            role=participant.role.value
        )

        segment = TranscriptSegment(
            timestamp=self.current_time,
            participant=participant.name,
            content=content,
            is_off_topic=self._is_content_off_topic(content),
            interruption=False,  # TODO: Implement interruption logic
            topics=self._extract_topics(content)
        )

        transcript.add_segment(segment)
        self.conversation_history.append(segment)

        # Update time and participant state
        self.current_time += speaking_time
        participant.speaking_time_used += speaking_time / 60  # Convert back to minutes

        # Check if this segment contains a question and handle it (but avoid recursion)
        if not hasattr(self, '_processing_question'):
            self._processing_question = False

        if not self._processing_question:
            self._handle_questions_in_segment(transcript, participant, content)

    def _add_segment_without_question_handling(self, transcript: MeetingTranscript, participant: BaseParticipant, content: str):
        """Add a segment without processing questions (to avoid recursion)"""
        # Calculate realistic speaking time based on content and personality
        from utils.speech_timing import calculate_speech_duration

        speaking_time = calculate_speech_duration(
            text=content,
            personality_type=participant.personality_type.value,
            role=participant.role.value
        )

        segment = TranscriptSegment(
            timestamp=self.current_time,
            participant=participant.name,
            content=content,
            is_off_topic=self._is_content_off_topic(content),
            interruption=False,
            topics=self._extract_topics(content)
        )

        transcript.add_segment(segment)
        self.conversation_history.append(segment)

        # Update time and participant state
        self.current_time += speaking_time
        participant.speaking_time_used += speaking_time / 60  # Convert back to minutes
    
    def _is_content_off_topic(self, content: str) -> bool:
        """Determine if content is off-topic for a standup"""
        off_topic_keywords = [
            # Personal/casual
            'game', 'coffee', 'weather', 'weekend', 'lunch', 'vacation', 'friday',
            'parking', 'office', 'team lunch', 'company policy', 'previous company',

            # Work-adjacent but not standup relevant
            'architecture refactoring', 'microservices', 'deployment process',
            'technical debt', 'office setup', 'article', 'interesting',

            # Conversation starters
            'by the way', 'speaking of', 'that reminds me', 'quick question',
            'did anyone', 'has anyone', 'how was', 'meant to ask'
        ]

        content_lower = content.lower()
        return any(keyword in content_lower for keyword in off_topic_keywords)
    
    def _extract_topics(self, content: str) -> List[str]:
        """Extract technical topics from content"""
        technical_keywords = [
            'api', 'database', 'frontend', 'backend', 'testing', 'bug',
            'feature', 'deployment', 'authentication', 'performance'
        ]
        
        topics = []
        content_lower = content.lower()
        for keyword in technical_keywords:
            if keyword in content_lower:
                topics.append(keyword)
        
        return topics
    
    def _handle_questions_in_segment(self, transcript: MeetingTranscript, speaker: BaseParticipant, content: str):
        """Handle questions in a segment and generate responses"""
        # Check if the content contains a question
        if not self._contains_question(content):
            return

        # Extract who the question is addressed to
        addressee = self._extract_question_addressee(content)

        if addressee:
            # Find the participant being addressed
            target_participant = self._find_participant_by_name(addressee)
            if target_participant and target_participant != speaker:
                console.print(f"💭 [blue]{target_participant.name} is responding to {speaker.name}...[/blue]")
                # Generate a response from the addressee
                response = self._generate_question_response(target_participant, speaker, content)
                if response:
                    self._add_segment_without_question_handling(transcript, target_participant, response)
        else:
            # Question not addressed to anyone specific, someone might volunteer to answer
            if random.random() < 0.6:  # 60% chance someone responds
                # Pick a random participant (not the speaker)
                potential_responders = [p for p in self.participants if p != speaker]
                if potential_responders:
                    responder = random.choice(potential_responders)
                    console.print(f"💭 [blue]{responder.name} is responding...[/blue]")
                    response = self._generate_question_response(responder, speaker, content)
                    if response:
                        self._add_segment_without_question_handling(transcript, responder, response)

    def _contains_question(self, content: str) -> bool:
        """Check if content contains a question"""
        question_indicators = [
            '?', 'what', 'how', 'when', 'where', 'why', 'who', 'which',
            'can you', 'could you', 'would you', 'do you', 'did you',
            'are you', 'is there', 'have you', 'will you'
        ]
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in question_indicators)

    def _extract_question_addressee(self, content: str) -> Optional[str]:
        """Extract who a question is addressed to"""
        content_lower = content.lower()

        # Look for direct addressing patterns
        for participant in self.participants:
            name_lower = participant.name.lower()

            # Patterns like "David, any updates...", "Hey Mike,", "@Alex"
            patterns = [
                f"{name_lower},",           # "David, any updates..."
                f"hey {name_lower}",        # "Hey David"
                f"hi {name_lower}",         # "Hi David"
                f"@{name_lower}",           # "@David"
                f"{name_lower}:",           # "David:"
                f"question for {name_lower}",  # "Question for David"
                f"ask {name_lower}",        # "Ask David"
                f"{name_lower} any",        # "David any updates"
                f"{name_lower} can",        # "David can you"
                f"{name_lower} do",         # "David do you"
                f"{name_lower} have",       # "David have you"
                f"{name_lower} are",        # "David are you"
            ]

            if any(pattern in content_lower for pattern in patterns):
                return participant.name

        return None

    def _find_participant_by_name(self, name: str) -> Optional[BaseParticipant]:
        """Find participant by name"""
        for participant in self.participants:
            if participant.name.lower() == name.lower():
                return participant
        return None

    def _generate_question_response(self, responder: BaseParticipant, questioner: BaseParticipant, question: str) -> str:
        """Generate a response to a question"""
        prompt = f"""Generate a response to this question from {questioner.name}:

        Question: "{question}"

        You are {responder.name}, a {responder.role.value} with {responder.personality_type.value} personality.
        Respond naturally and helpfully to the question. Keep it conversational and relevant to a standup meeting.
        Be specific and provide useful technical information if appropriate.
        """

        # Always use Gemini - no fallbacks for questions
        return self._generate_with_gemini(responder, prompt)

    def _build_conversation_context(self) -> Dict[str, Any]:
        """Build context for AI generation"""
        return {
            'meeting_phase': self.current_phase,
            'elapsed_time': self.current_time / 60,  # minutes
            'recent_statements': [s.content for s in self.conversation_history[-3:]],
            'active_topics': self.active_topics,
            'meeting_style': self.config['meeting']['style']
        }
    
    def _generate_blocker_statement(self, participant: BaseParticipant) -> str:
        """Generate a blocker statement"""
        prompt = f"""Generate a realistic blocker statement for {participant.name}, a {participant.role.value}.
        Mention a specific technical issue or dependency that's blocking progress.
        Keep it relevant to their role and current work context.
        """
        return self._generate_with_gemini(participant, prompt)

    def _generate_closing_statement(self, participant: BaseParticipant) -> str:
        """Generate closing statement"""
        prompt = f"""Generate a brief closing statement to wrap up the daily standup.
        You are {participant.name}, a {participant.role.value}.
        Keep it professional and appropriate for ending the meeting.
        """
        return self._generate_with_gemini(participant, prompt)
    
    def _handle_follow_up_interactions(self, transcript: MeetingTranscript, speaker: BaseParticipant):
        """Handle follow-up questions and interactions"""
        # Simple implementation - can be enhanced
        if random.random() < 0.3:  # 30% chance of follow-up
            questioner = random.choice([p for p in self.participants if p != speaker])
            question = f"Quick question about that - can you clarify the timeline?"
            self._add_segment(transcript, questioner, question)
    
    def _handle_blocker_discussion(self, transcript: MeetingTranscript, speaker: BaseParticipant):
        """Handle discussion around blockers"""
        # Simple implementation
        if random.random() < 0.5:  # 50% chance of response
            helper = random.choice([p for p in self.participants if p != speaker])
            response = f"I can help with that after the meeting."
            self._add_segment(transcript, helper, response)
    
    def _create_participant_from_config(self, config: Dict[str, Any]) -> BaseParticipant:
        """Create a participant instance from configuration"""
        # For now, create a simple participant
        # TODO: Implement specific personality classes
        from participants.simple_participant import SimpleParticipant
        
        name = config['name']
        role = Role(config['role'])
        personality_type = PersonalityType(config['personality'])
        
        traits_config = config.get('traits', {})
        traits = PersonalityTraits(**traits_config)
        
        return SimpleParticipant(name, role, personality_type, traits)
    
    def _generate_participant_config(self, index: int) -> Dict[str, Any]:
        """Generate a participant configuration"""
        names = ["Alice", "Bob", "Charlie", "Diana", "Eve", "Frank"]
        roles = list(Role)
        personalities = list(PersonalityType)
        
        return {
            'name': names[index % len(names)],
            'role': random.choice(roles).value,
            'personality': random.choice(personalities).value,
            'traits': {
                'verbosity': random.uniform(0.3, 0.8),
                'technical_detail': random.uniform(0.2, 0.9),
                'interruption_tendency': random.uniform(0.1, 0.5),
                'curiosity': random.uniform(0.3, 0.8),
                'off_topic_probability': random.uniform(0.1, 0.4),
                'time_awareness': random.uniform(0.4, 0.9)
            }
        }
