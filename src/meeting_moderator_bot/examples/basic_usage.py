"""
Basic usage example for Meeting Moderator Bo<PERSON>

Demonstrates how to use the Meeting Moderator Bot in a simple scenario
without Teams integration.
"""

import os
import time
import logging
from typing import List

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the moderator components
from ..core.moderator import MeetingModerator
from ..utils.config import ModeratorConfig


def simulate_standup_meeting():
    """Simulate a standup meeting with various scenarios"""
    
    # Load configuration
    config = ModeratorConfig()
    
    # Get API key (in real usage, this would come from environment variables)
    api_key = os.getenv('GEMINI_API_KEY', 'your_api_key_here')

    if api_key == 'your_api_key_here':
        logger.warning("Please set GEMINI_API_KEY environment variable for AI features")
        logger.info("Using gemini-2.5-flash model for enhanced performance")
        # Disable AI classification for demo
        config.classification.use_ai_classification = False
    else:
        logger.info("Using gemini-2.5-flash model for AI-powered analysis")
    
    # Initialize moderator with team ID for learning
    team_id = "demo_team_001"
    moderator = MeetingModerator(config, api_key, team_id)
    
    # Define participants
    participants = ["<PERSON>", "<PERSON>", "Charlie", "David"]
    
    # Start the meeting
    print("🎭 Starting Meeting Moderation Demo")
    print("=" * 50)
    
    moderator.start_meeting(participants)
    
    # Print welcome events
    for event in moderator.moderation_events:
        print(f"🤖 Bot: {event.message}")
    
    print("\n📋 Meeting Started - Participants:", ", ".join(participants))
    print("-" * 50)
    
    # Simulate meeting conversation
    conversation_scenarios = [
        # Good standup updates
        ("Alice", "Yesterday I completed the user authentication module. Today I'll work on the password reset feature. No blockers."),
        ("Bob", "I finished the database migration yesterday. Today I'm starting on the API endpoints. I'm blocked on getting access to the staging environment."),
        
        # Off-topic discussion
        ("Charlie", "Speaking of staging, did you guys see the new restaurant that opened downtown? I went there for lunch yesterday and the food was amazing!"),
        
        # Technical deep dive
        ("David", "Actually, about that database migration Bob mentioned, I think we should discuss the indexing strategy. The current approach might cause performance issues when we scale. We need to consider composite indexes on the user_id and timestamp columns, and maybe implement partitioning..."),
        
        # More good updates
        ("Charlie", "Oh right, sorry. Yesterday I worked on the frontend components. Today I'll integrate them with Bob's API. No blockers."),
        
        # Problem solving session
        ("Alice", "About David's performance concern, maybe we should analyze the query patterns first. We could use the slow query log to identify bottlenecks, then decide on the indexing strategy. What do you think about using a monitoring tool like New Relic?"),
        
        # Time management - long update
        ("Bob", "So regarding the API endpoints, I've been thinking about the architecture and I believe we should implement a microservices approach. Each service would handle a specific domain, like user management, content management, and analytics. This would give us better scalability and maintainability. We'd need to set up service discovery, load balancing, and implement proper error handling between services. Also, we should consider using GraphQL instead of REST for better data fetching efficiency..."),
    ]
    
    # Process each conversation segment
    for i, (speaker, text) in enumerate(conversation_scenarios, 1):
        print(f"\n{i}. {speaker}: {text}")
        
        # Add small delay to simulate real conversation timing
        time.sleep(0.5)
        
        # Process the speech
        events = moderator.process_speech(speaker, text)
        
        # Display any moderation events
        for event in events:
            if event.event_type == 'interruption':
                print(f"   🚨 Bot interrupts: {event.message}")
            elif event.event_type == 'suggestion':
                print(f"   💡 Bot suggests: {event.message}")
            elif event.event_type == 'phase_change':
                print(f"   📋 Bot: {event.message}")
        
        # Show current statistics
        if i % 3 == 0:  # Every 3rd message
            stats = moderator.get_statistics()
            print(f"   📊 Stats: {stats['total_interruptions']} interruptions, "
                  f"{stats['off_topic_segments']} off-topic segments")
    
    # End the meeting
    print("\n" + "=" * 50)
    print("🏁 Ending Meeting")
    
    summary = moderator.end_meeting()
    
    # Display meeting summary
    print("\n📊 Meeting Summary:")
    print(f"   Duration: {summary['duration_minutes']:.1f} minutes")
    print(f"   Total interruptions: {summary['total_interruptions']}")
    print(f"   Off-topic segments: {summary['off_topic_segments']}")
    print(f"   Participants who updated: {summary['participants_who_updated']}/{summary['total_participants']}")
    print(f"   Effectiveness score: {summary['effectiveness_score']:.2f}/1.0")
    print(f"   Total moderation events: {summary['moderation_events']}")

    # Demonstrate feedback collection
    print("\n🔄 Collecting Feedback:")

    # Simulate participant feedback
    moderator.collect_feedback(
        feedback_type="interruption",
        rating=1,  # Positive feedback
        participant="Alice",
        context={"interruption_reason": "off_topic", "timing": "appropriate"},
        comment="The bot helped keep us focused"
    )

    moderator.collect_feedback(
        feedback_type="suggestion",
        rating=-1,  # Negative feedback
        participant="Bob",
        context={"suggestion_type": "time_management"},
        comment="Too many suggestions, felt intrusive"
    )

    moderator.collect_feedback(
        feedback_type="overall",
        rating=2,  # Very positive
        participant="Charlie",
        context={"meeting_effectiveness": summary['effectiveness_score']},
        comment="Great meeting, stayed on track"
    )

    print("   ✅ Feedback collected from participants")

    # Show team analysis
    team_analysis = moderator.get_team_analysis()
    print(f"\n🧠 Team Analysis:")
    print(f"   Satisfaction Score: {team_analysis.get('satisfaction_score', 0):.2f}")
    print(f"   Feedback Count: {team_analysis.get('feedback_count', 0)}")
    if team_analysis.get('recommendations'):
        print("   Recommendations:")
        for rec in team_analysis['recommendations'][:2]:  # Show first 2
            print(f"     • {rec}")

    # Show learning analytics
    stats = moderator.get_statistics()
    if 'learning_analytics' in stats:
        learning_stats = stats['learning_analytics']
        print(f"\n📈 Learning Analytics:")
        print(f"   Total Feedback: {learning_stats['learning_stats']['total_feedback']}")
        print(f"   Teams Learning: {learning_stats['team_count']}")
        print(f"   Average Satisfaction: {learning_stats['average_satisfaction']:.2f}")

    # Show current adaptations
    if 'team_adaptations' in stats:
        adaptations = stats['team_adaptations']
        print(f"\n⚙️ Current Team Adaptations:")
        print(f"   Personality: {adaptations.get('personality', 'professional')}")
        print(f"   Interruption Threshold: {adaptations.get('interruption_threshold', 0.7):.2f}")
        print(f"   Suggestion Frequency: {adaptations.get('suggestion_frequency_modifier', 0.5):.2f}")


def demonstrate_configuration():
    """Demonstrate different configuration options"""
    
    print("\n🔧 Configuration Demo")
    print("=" * 30)
    
    # Create different personality configurations
    personalities = ['professional', 'friendly', 'assertive']
    
    for personality in personalities:
        print(f"\n{personality.title()} Bot Personality:")
        
        config = ModeratorConfig()
        config.bot_personality = personality
        config.classification.use_ai_classification = False  # For demo
        
        moderator = MeetingModerator(config, "demo_key")
        moderator.start_meeting(["Alice", "Bob"])
        
        # Show welcome message style
        welcome_event = moderator.moderation_events[0]
        print(f"   Welcome: {welcome_event.message}")
        
        # Simulate an interruption
        from ..core.topic_classifier import TopicClassification
        from ..core.interruption_engine import InterruptionDecision, InterruptionLevel
        
        # Mock classification and decision
        classification = TopicClassification(
            topic_type='off_topic_discussion',
            is_standup_relevant=False,
            confidence=0.8,
            keywords=['lunch', 'restaurant'],
            technical_indicators=[],
            reasoning="Off-topic discussion about food"
        )
        
        decision = InterruptionDecision(
            should_interrupt=True,
            confidence=0.8,
            level=InterruptionLevel.POLITE_REDIRECT,
            reason="Off-topic discussion",
            suggested_message="Let's get back to standup updates"
        )
        
        # Create interruption event
        interruption_event = moderator._create_interruption_event(
            "Alice", decision, classification, time.time()
        )
        
        print(f"   Interruption: {interruption_event.message}")


def test_topic_classification():
    """Test topic classification with various examples"""
    
    print("\n🔍 Topic Classification Demo")
    print("=" * 35)
    
    config = ModeratorConfig()
    config.classification.use_ai_classification = False  # Use rule-based for demo
    
    from ..core.topic_classifier import TopicClassifier
    
    classifier = TopicClassifier("demo_key", config.classification)
    
    test_texts = [
        ("Yesterday I worked on the login feature", "Good standup update"),
        ("The database performance is terrible, we need to optimize the queries and add indexes", "Technical deep dive"),
        ("Did anyone watch the game last night?", "Off-topic discussion"),
        ("I think we should redesign the entire architecture", "Problem solving"),
        ("I'm blocked on getting API access", "Blocker discussion"),
        ("What exactly do you mean by 'user experience'?", "Clarification question")
    ]
    
    for text, expected in test_texts:
        classification = classifier.classify_text(text, "individual_updates")
        
        print(f"\nText: '{text}'")
        print(f"   Expected: {expected}")
        print(f"   Classified as: {classification.topic_type}")
        print(f"   Standup relevant: {classification.is_standup_relevant}")
        print(f"   Confidence: {classification.confidence:.2f}")
        print(f"   Reasoning: {classification.reasoning}")

        # Show enhanced NLP analysis if available
        if classification.intent_analysis:
            intent = classification.intent_analysis
            print(f"   Intent: {intent.primary_intent} (confidence: {intent.confidence:.2f})")
            print(f"   Sentiment: {intent.sentiment}")
            if intent.entities:
                print(f"   Entities: {', '.join(intent.entities[:3])}")  # Show first 3

        if classification.semantic_analysis:
            semantic = classification.semantic_analysis
            print(f"   Complexity Score: {semantic.complexity_score:.2f}")
            print(f"   Coherence Score: {semantic.coherence_score:.2f}")
            if semantic.main_topics:
                print(f"   Main Topics: {', '.join(semantic.main_topics[:3])}")  # Show first 3


def test_suggestion_engine():
    """Test the suggestion engine with various scenarios"""

    print("\n💡 Suggestion Engine Demo")
    print("=" * 35)

    config = ModeratorConfig()
    config.classification.use_ai_classification = False  # Use rule-based for demo

    from ..core.suggestion_engine import SuggestionEngine, SuggestionConfig
    from ..core.topic_classifier import TopicClassifier, TopicClassification
    from ..core.conversation_monitor import ConversationMonitor, ConversationState

    # Initialize components
    suggestion_config = SuggestionConfig(enable_ai_suggestions=False)
    suggestion_engine = SuggestionEngine("demo_key", suggestion_config)

    # Test scenarios
    scenarios = [
        {
            "name": "Off-topic Discussion",
            "classification": TopicClassification(
                topic_type='off_topic_discussion',
                is_standup_relevant=False,
                confidence=0.8,
                keywords=['lunch', 'restaurant'],
                technical_indicators=[],
                reasoning="Off-topic discussion about food"
            ),
            "conversation_state": ConversationState(
                current_speaker="Alice",
                current_speaker_start_time=0.0,
                current_speaker_duration=45.0,
                total_duration=300.0,
                total_segments=5,
                participants_spoken=["Alice", "Bob"],
                context="Discussion about restaurants",
                recent_topics=["food", "lunch"],
                speaking_pattern={"Alice": 120.0, "Bob": 60.0, "Charlie": 0.0}
            )
        },
        {
            "name": "Long Meeting",
            "classification": TopicClassification(
                topic_type='standup_update',
                is_standup_relevant=True,
                confidence=0.9,
                keywords=['working', 'today'],
                technical_indicators=[],
                reasoning="Valid standup update"
            ),
            "conversation_state": ConversationState(
                current_speaker="Bob",
                current_speaker_start_time=0.0,
                current_speaker_duration=30.0,
                total_duration=1200.0,  # 20 minutes
                total_segments=15,
                participants_spoken=["Alice", "Bob", "Charlie"],
                context="Regular standup updates",
                recent_topics=["features", "testing"],
                speaking_pattern={"Alice": 300.0, "Bob": 400.0, "Charlie": 200.0}
            )
        }
    ]

    for scenario in scenarios:
        print(f"\n📋 Scenario: {scenario['name']}")

        suggestions = suggestion_engine.generate_suggestions(
            classification=scenario['classification'],
            conversation_state=scenario['conversation_state'],
            meeting_phase="individual_updates",
            current_participant=scenario['conversation_state'].current_speaker
        )

        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                print(f"   {i}. {suggestion.suggestion_type.value}")
                print(f"      Message: {suggestion.message}")
                print(f"      Priority: {suggestion.priority}/5")
                print(f"      Confidence: {suggestion.confidence:.2f}")
                print(f"      Timing: {suggestion.timing}")
        else:
            print("   No suggestions generated")

    # Show suggestion analytics
    analytics = suggestion_engine.get_suggestion_analytics()
    print(f"\n📊 Suggestion Analytics:")
    print(f"   Total Suggestions: {analytics['total_suggestions']}")
    print(f"   Implemented: {analytics['implemented_suggestions']}")
    if analytics['most_common_type']:
        print(f"   Most Common Type: {analytics['most_common_type']}")


if __name__ == "__main__":
    """Run the demo"""
    
    try:
        # Run the main simulation
        simulate_standup_meeting()
        
        # Demonstrate configuration options
        demonstrate_configuration()
        
        # Test topic classification
        test_topic_classification()

        # Test suggestion engine
        test_suggestion_engine()

        print("\n✅ Demo completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")
        print("\nMake sure you have set up the environment correctly.")
        print("For AI features, set GEMINI_API_KEY environment variable.")
