# Default Configuration for Meeting Moderator Bot
# This file contains all configurable settings for the bot

# General <PERSON><PERSON> Settings
bot_name: "Meeting Moderator"
bot_personality: "professional"  # professional, friendly, assertive
language: "en"

# Meeting Settings
default_meeting_duration: 900.0  # 15 minutes in seconds
max_meeting_duration: 1800.0     # 30 minutes in seconds
standup_format: "standard"       # standard, scrum, custom

# Integration Settings
enable_teams_integration: false
enable_slack_integration: false
enable_webhook_integration: true

# Logging and Monitoring
log_level: "INFO"
enable_analytics: true
analytics_endpoint: null

# AI Settings
gemini_model: "gemini-2.5-flash"
ai_confidence_threshold: 0.7
enable_learning: true

# Topic Classification Settings
classification:
  confidence_threshold: 0.7
  technical_keywords_weight: 0.3
  context_window_size: 5
  use_ai_classification: true
  gemini_model: "gemini-2.5-flash"

# Interruption Engine Settings
interruption:
  confidence_threshold: 0.7
  max_off_topic_duration: 30.0      # seconds
  max_technical_duration: 45.0      # seconds
  escalation_time: 15.0              # seconds between escalation levels
  participant_speaking_limit: 120.0  # 2 minutes max per participant
  politeness_factor: 0.8             # How polite to be (0-1)

# Conversation Monitoring Settings
monitoring:
  context_window_size: 10
  max_context_length: 1000
  speaking_time_threshold: 120.0
  engagement_tracking: true
  topic_extraction: true

# Message Templates by Personality
message_templates:
  professional:
    welcome: "Hello! I'm here to help keep our standup meeting focused and efficient. Please share your daily updates."
    interruption_gentle: "Excuse me, {participant}. Could we keep this brief and focus on standup updates?"
    interruption_firm: "Let's get back to our standup format. We can discuss this in detail after the meeting."
    technical_redirect: "This sounds like a technical discussion. Should we schedule a separate session for this?"
    time_warning: "We're running over time. Let's wrap up the remaining updates quickly."
    meeting_end: "Thank you everyone! The standup is complete. Please follow up on any blockers mentioned."
  
  friendly:
    welcome: "Hi everyone! 😊 I'm here to help keep our standup on track. Let's share our updates!"
    interruption_gentle: "Hey {participant}, could we keep this focused on standup updates? Thanks!"
    interruption_firm: "Let's circle back to our standup format - we can chat about this afterwards!"
    technical_redirect: "Ooh, interesting technical stuff! Should we park this for a tech discussion later?"
    time_warning: "Time check! ⏰ Let's wrap up the remaining updates."
    meeting_end: "Great standup everyone! 👏 Don't forget to follow up on those blockers."
  
  assertive:
    welcome: "Good morning. I will be moderating this standup to ensure we stay on schedule and on topic."
    interruption_gentle: "{participant}, please focus on your standup update."
    interruption_firm: "We need to return to the standup format immediately."
    technical_redirect: "Technical discussions belong in a separate meeting. Please note this as a blocker."
    time_warning: "Time limit exceeded. Conclude your updates now."
    meeting_end: "Standup complete. Address blockers in follow-up meetings."

# Topic Classification Keywords
topic_keywords:
  standup_relevant:
    - "yesterday"
    - "today"
    - "tomorrow"
    - "working on"
    - "completed"
    - "finished"
    - "blocked"
    - "blocker"
    - "impediment"
    - "help"
    - "stuck"
    - "issue"
    - "progress"
    - "update"
    - "status"
    - "done"
    - "doing"
    - "will do"
    - "planning"
    - "scheduled"
    - "meeting"
    - "review"
    - "testing"
  
  technical_indicators:
    - "algorithm"
    - "implementation"
    - "architecture"
    - "database"
    - "query"
    - "optimization"
    - "performance"
    - "memory"
    - "cpu"
    - "latency"
    - "framework"
    - "library"
    - "dependency"
    - "version"
    - "configuration"
    - "deployment"
    - "infrastructure"
    - "server"
    - "network"
    - "security"
    - "authentication"
    - "authorization"
    - "encryption"
    - "protocol"
    - "api"
    - "endpoint"
    - "request"
    - "response"
    - "json"
    - "xml"
    - "debugging"
    - "logging"
    - "monitoring"
    - "metrics"
    - "analytics"
  
  off_topic_indicators:
    - "weekend"
    - "vacation"
    - "holiday"
    - "weather"
    - "lunch"
    - "coffee"
    - "movie"
    - "tv"
    - "sports"
    - "game"
    - "family"
    - "personal"
    - "politics"
    - "news"
    - "gossip"
    - "rumor"
    - "joke"
    - "funny"
  
  problem_solving_indicators:
    - "solution"
    - "approach"
    - "strategy"
    - "design"
    - "architecture"
    - "brainstorm"
    - "idea"
    - "suggestion"
    - "proposal"
    - "alternative"
    - "option"
    - "choice"
    - "decision"
    - "analysis"
    - "evaluation"

# Performance Settings
performance:
  max_concurrent_meetings: 100
  response_timeout: 2.0  # seconds
  cache_size: 1000
  cleanup_interval: 300  # 5 minutes

# Security Settings
security:
  enable_rate_limiting: true
  max_requests_per_minute: 60
  enable_input_validation: true
  max_message_length: 5000
  allowed_file_types: []

# Development Settings
development:
  debug_mode: false
  mock_ai_responses: false
  log_all_events: false
  enable_test_endpoints: false
