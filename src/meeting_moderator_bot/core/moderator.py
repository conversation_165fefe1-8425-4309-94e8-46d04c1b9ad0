"""
Meeting Moderator - Main orchestrator class

Coordinates all components to provide intelligent meeting moderation.
Monitors conversations, classifies topics, and manages interruptions.
"""

import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from .topic_classifier import TopicClassifier, TopicClassification
from .interruption_engine import InterruptionEngine, InterruptionDecision
from .conversation_monitor import ConversationMonitor, ConversationState
from .suggestion_engine import SuggestionEngine, Suggestion, SuggestionConfig
from .learning_system import LearningSystem, LearningConfig, FeedbackType, FeedbackRating
from .performance_optimizer import PerformanceOptimizer, CacheConfig, performance_timer
from ..utils.config import ModeratorConfig

logger = logging.getLogger(__name__)


class MeetingPhase(Enum):
    """Meeting phases for standup meetings"""
    OPENING = "opening"
    INDIVIDUAL_UPDATES = "individual_updates"
    BLOCKERS = "blockers"
    CLOSING = "closing"
    OFF_TOPIC = "off_topic"


@dataclass
class ModerationEvent:
    """Event generated by the moderator"""
    timestamp: float
    event_type: str  # 'interruption', 'suggestion', 'phase_change'
    participant: Optional[str]
    message: str
    confidence: float
    context: Dict[str, Any]


class MeetingModerator:
    """
    Main Meeting Moderator class
    
    Orchestrates conversation monitoring, topic classification, and interruption
    management to keep standup meetings focused and productive.
    """
    
    def __init__(self, config: ModeratorConfig, gemini_api_key: str, team_id: Optional[str] = None):
        """
        Initialize the meeting moderator

        Args:
            config: Moderator configuration
            gemini_api_key: API key for Gemini AI services
            team_id: Optional team identifier for learning
        """
        self.config = config
        self.gemini_api_key = gemini_api_key
        self.team_id = team_id or "default_team"

        # Initialize core components
        self.topic_classifier = TopicClassifier(gemini_api_key, config.classification)
        self.interruption_engine = InterruptionEngine(config.interruption)
        self.conversation_monitor = ConversationMonitor(config.monitoring)

        # Initialize suggestion engine
        suggestion_config = SuggestionConfig()
        self.suggestion_engine = SuggestionEngine(gemini_api_key, suggestion_config)

        # Initialize learning system
        learning_config = LearningConfig(enable_learning=config.enable_learning)
        self.learning_system = LearningSystem(learning_config)

        # Initialize performance optimizer
        cache_config = CacheConfig(
            enable_caching=True,
            max_cache_size=1000,
            cache_ttl_seconds=300.0
        )
        self.performance_optimizer = PerformanceOptimizer(cache_config)

        # Apply team-specific adaptations
        self._apply_team_adaptations()
        
        # Meeting state
        self.current_phase = MeetingPhase.OPENING
        self.meeting_start_time = None
        self.participants = {}
        self.moderation_events = []
        
        # Statistics
        self.stats = {
            'total_interruptions': 0,
            'successful_redirections': 0,
            'off_topic_segments': 0,
            'meeting_duration': 0.0
        }
        
        logger.info("Meeting Moderator initialized successfully")
    
    def start_meeting(self, participants: List[str]) -> None:
        """
        Start monitoring a new meeting
        
        Args:
            participants: List of participant names
        """
        self.meeting_start_time = time.time()
        self.current_phase = MeetingPhase.OPENING
        self.participants = {name: {'update_given': False, 'speaking_time': 0.0} 
                           for name in participants}
        self.moderation_events = []
        
        logger.info(f"Meeting started with {len(participants)} participants")
        
        # Send welcome message
        welcome_event = ModerationEvent(
            timestamp=self.meeting_start_time,
            event_type='suggestion',
            participant=None,
            message=self._get_welcome_message(),
            confidence=1.0,
            context={'phase': self.current_phase.value}
        )
        self.moderation_events.append(welcome_event)
    
    def process_speech(self, participant: str, text: str) -> List[ModerationEvent]:
        """
        Process a participant's speech and generate moderation events

        Args:
            participant: Name of the speaking participant
            text: Transcribed speech text

        Returns:
            List of moderation events (interruptions, suggestions, etc.)
        """
        # Track performance
        start_time = time.time()
        success = True

        try:
            current_time = time.time()
            events = []

            # Update conversation monitor
            conversation_state = self.conversation_monitor.add_speech(
                participant, text, current_time
            )

            # Update participant speaking time
            if participant in self.participants:
                # Estimate speaking time (rough calculation)
                estimated_duration = len(text.split()) * 0.5  # ~0.5 seconds per word
                self.participants[participant]['speaking_time'] += estimated_duration

            # Classify the topic
            classification = self.topic_classifier.classify_text(
                text, self.current_phase.value, conversation_state.context
            )

            # Check if interruption is needed
            interruption_decision = self.interruption_engine.should_interrupt(
                classification, conversation_state, self.current_phase
            )

            if interruption_decision.should_interrupt:
                interruption_event = self._create_interruption_event(
                    participant, interruption_decision, classification, current_time
                )
                events.append(interruption_event)
                self.stats['total_interruptions'] += 1

            # Generate contextual suggestions
            suggestions = self.suggestion_engine.generate_suggestions(
                classification, conversation_state, self.current_phase.value, participant
            )

            # Convert suggestions to moderation events
            for suggestion in suggestions:
                suggestion_event = self._create_suggestion_event(suggestion, current_time)
                events.append(suggestion_event)

            # Check for phase transitions
            phase_event = self._check_phase_transition(conversation_state, current_time)
            if phase_event:
                events.append(phase_event)

            # Check for time management suggestions
            time_event = self._check_time_management(participant, current_time)
            if time_event:
                events.append(time_event)

            # Update statistics
            if not classification.is_standup_relevant:
                self.stats['off_topic_segments'] += 1

            return events

        except Exception as e:
            success = False
            logger.error(f"Error processing speech: {e}")
            raise
        finally:
            # Track performance metrics
            processing_time = time.time() - start_time
            self.performance_optimizer.track_request(processing_time, success)
    
    def end_meeting(self) -> Dict[str, Any]:
        """
        End the meeting and return summary statistics
        
        Returns:
            Dictionary with meeting statistics and summary
        """
        if self.meeting_start_time:
            self.stats['meeting_duration'] = time.time() - self.meeting_start_time
        
        # Generate meeting summary
        summary = {
            'duration_minutes': self.stats['meeting_duration'] / 60,
            'total_interruptions': self.stats['total_interruptions'],
            'off_topic_segments': self.stats['off_topic_segments'],
            'participants_who_updated': sum(1 for p in self.participants.values() 
                                          if p['update_given']),
            'total_participants': len(self.participants),
            'moderation_events': len(self.moderation_events),
            'effectiveness_score': self._calculate_effectiveness_score()
        }
        
        logger.info(f"Meeting ended. Duration: {summary['duration_minutes']:.1f} minutes")
        return summary
    
    def _create_interruption_event(self, participant: str, decision: InterruptionDecision,
                                 classification: TopicClassification, timestamp: float) -> ModerationEvent:
        """Create an interruption event with appropriate message"""
        
        # Generate contextual interruption message
        if classification.topic_type == 'technical_deep_dive':
            message = f"Excuse me, {participant}. This sounds like a technical issue that might need detailed discussion. Could we note this as a blocker and schedule a separate technical session? Let's keep the standup focused on your daily updates."
        elif classification.topic_type == 'off_topic_discussion':
            message = f"I notice we're getting into some detailed discussion, {participant}. Should we park this for after the standup and continue with our daily updates?"
        elif classification.topic_type == 'problem_solving':
            message = f"{participant}, this seems like problem-solving that might need more time. Could we add this to our parking lot and address it in a follow-up meeting?"
        else:
            message = f"Let's try to keep our standup focused, {participant}. Could you summarize your update in terms of what you did yesterday, what you're doing today, and any blockers?"
        
        return ModerationEvent(
            timestamp=timestamp,
            event_type='interruption',
            participant=participant,
            message=message,
            confidence=decision.confidence,
            context={
                'reason': decision.reason,
                'topic_type': classification.topic_type,
                'phase': self.current_phase.value
            }
        )

    def _create_suggestion_event(self, suggestion: Suggestion, timestamp: float) -> ModerationEvent:
        """Create a moderation event from a suggestion"""

        return ModerationEvent(
            timestamp=timestamp,
            event_type='suggestion',
            participant=suggestion.context.get('participant'),
            message=suggestion.message,
            confidence=suggestion.confidence,
            context={
                'suggestion_type': suggestion.suggestion_type.value,
                'priority': suggestion.priority,
                'timing': suggestion.timing,
                'reasoning': suggestion.reasoning,
                'phase': self.current_phase.value
            }
        )
    
    def _check_phase_transition(self, conversation_state: ConversationState, 
                              timestamp: float) -> Optional[ModerationEvent]:
        """Check if we should transition to a new meeting phase"""
        
        # Simple phase transition logic
        if (self.current_phase == MeetingPhase.OPENING and 
            conversation_state.total_segments > 2):
            self.current_phase = MeetingPhase.INDIVIDUAL_UPDATES
            return ModerationEvent(
                timestamp=timestamp,
                event_type='phase_change',
                participant=None,
                message="Let's start with individual updates. Who would like to go first?",
                confidence=0.9,
                context={'new_phase': self.current_phase.value}
            )
        
        return None
    
    def _check_time_management(self, participant: str, timestamp: float) -> Optional[ModerationEvent]:
        """Check if time management suggestions are needed"""
        
        if participant in self.participants:
            speaking_time = self.participants[participant]['speaking_time']
            
            # If someone has been speaking for more than 2 minutes
            if speaking_time > 120:  # 2 minutes
                return ModerationEvent(
                    timestamp=timestamp,
                    event_type='suggestion',
                    participant=participant,
                    message=f"{participant}, you've covered a lot of ground. Could you wrap up your update so we can hear from others?",
                    confidence=0.8,
                    context={'speaking_time': speaking_time}
                )
        
        return None
    
    def _get_welcome_message(self) -> str:
        """Get welcome message for meeting start"""
        return ("Good morning everyone! I'm here to help keep our standup focused and efficient. "
                "Please share your updates on what you did yesterday, what you're doing today, "
                "and any blockers you have. Let's get started!")
    
    def _calculate_effectiveness_score(self) -> float:
        """Calculate meeting effectiveness score (0-1)"""
        if not self.meeting_start_time:
            return 0.0
        
        # Simple scoring based on various factors
        duration_score = max(0, 1 - (self.stats['meeting_duration'] / 60 - 15) / 15)  # Optimal ~15 min
        interruption_score = max(0, 1 - self.stats['total_interruptions'] / 10)  # Fewer interruptions better
        topic_score = max(0, 1 - self.stats['off_topic_segments'] / 20)  # Fewer off-topic better
        
        return (duration_score + interruption_score + topic_score) / 3
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get current meeting statistics"""
        current_duration = 0.0
        if self.meeting_start_time:
            current_duration = time.time() - self.meeting_start_time
        
        return {
            **self.stats,
            'current_duration': current_duration,
            'current_phase': self.current_phase.value,
            'participants': self.participants.copy(),
            'effectiveness_score': self._calculate_effectiveness_score(),
            'team_adaptations': self.learning_system.get_team_adaptations(self.team_id),
            'learning_analytics': self.learning_system.get_learning_analytics(),
            'performance_metrics': self.performance_optimizer.get_performance_metrics().__dict__,
            'cache_statistics': self.performance_optimizer.get_cache_statistics()
        }

    def collect_feedback(self,
                        feedback_type: str,
                        rating: int,
                        participant: str,
                        context: Dict[str, Any],
                        comment: Optional[str] = None,
                        bot_action_id: Optional[str] = None) -> None:
        """
        Collect feedback from participants

        Args:
            feedback_type: Type of feedback ('interruption', 'suggestion', 'overall', 'personality', 'timing')
            rating: Rating from -2 (very negative) to 2 (very positive)
            participant: Name of participant giving feedback
            context: Context information
            comment: Optional text comment
            bot_action_id: Optional bot action identifier
        """
        try:
            feedback_type_enum = FeedbackType(f"{feedback_type}_feedback")
            rating_enum = FeedbackRating(rating)

            self.learning_system.collect_feedback(
                feedback_type=feedback_type_enum,
                rating=rating_enum,
                participant=participant,
                team_id=self.team_id,
                context=context,
                comment=comment,
                bot_action_id=bot_action_id
            )

            # Re-apply adaptations after feedback
            self._apply_team_adaptations()

        except (ValueError, KeyError) as e:
            logger.warning(f"Invalid feedback parameters: {e}")

    def get_team_analysis(self) -> Dict[str, Any]:
        """Get analysis of team patterns and recommendations"""
        return self.learning_system.analyze_team_patterns(self.team_id)

    def _apply_team_adaptations(self):
        """Apply team-specific adaptations from learning system"""

        adaptations = self.learning_system.get_team_adaptations(self.team_id)

        # Apply personality adaptation
        if 'personality' in adaptations:
            self.config.bot_personality = adaptations['personality']

        # Apply interruption threshold adaptation
        if 'interruption_threshold' in adaptations:
            self.interruption_engine.config.confidence_threshold = adaptations['interruption_threshold']

        # Apply suggestion frequency adaptation
        if 'suggestion_frequency_modifier' in adaptations:
            self.suggestion_engine.config.max_suggestions_per_minute = int(
                3 * adaptations['suggestion_frequency_modifier']
            )

        # Apply technical tolerance adaptation
        if 'technical_tolerance' in adaptations:
            self.interruption_engine.config.max_technical_duration = (
                45.0 * (1 + adaptations['technical_tolerance'])
            )

        # Apply politeness factor adaptation
        if 'politeness_factor' in adaptations:
            self.interruption_engine.config.politeness_factor = adaptations['politeness_factor']

        logger.debug(f"Applied team adaptations for {self.team_id}: {adaptations}")
