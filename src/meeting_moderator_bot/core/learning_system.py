"""
Learning and Adaptation System

Implements feedback collection, team-specific adaptations, and continuous
improvement for the Meeting Moderator Bot.
"""

import logging
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)


class FeedbackType(Enum):
    """Types of feedback the system can receive"""
    INTERRUPTION_FEEDBACK = "interruption_feedback"  # Was interruption appropriate?
    SUGGESTION_FEEDBACK = "suggestion_feedback"      # Was suggestion helpful?
    OVERALL_MEETING = "overall_meeting"              # Overall meeting quality
    PERSONALITY_FEEDBACK = "personality_feedback"     # Bot personality feedback
    TIMING_FEEDBACK = "timing_feedback"              # Timing of interventions


class FeedbackRating(Enum):
    """Feedback rating scale"""
    VERY_NEGATIVE = -2
    NEGATIVE = -1
    NEUTRAL = 0
    POSITIVE = 1
    VERY_POSITIVE = 2


@dataclass
class FeedbackEntry:
    """Individual feedback entry"""
    feedback_type: FeedbackType
    rating: FeedbackRating
    timestamp: float
    participant: str
    team_id: str
    context: Dict[str, Any]
    comment: Optional[str] = None
    
    # Metadata
    meeting_id: Optional[str] = None
    bot_action_id: Optional[str] = None


@dataclass
class TeamProfile:
    """Team-specific behavior profile"""
    team_id: str
    team_name: str
    
    # Preferences learned from feedback
    preferred_personality: str = "professional"  # professional, friendly, assertive
    interruption_sensitivity: float = 0.7  # 0-1, higher = more sensitive to interruptions
    suggestion_frequency: float = 0.5  # 0-1, how often they want suggestions
    technical_tolerance: float = 0.5  # 0-1, tolerance for technical discussions
    
    # Behavioral patterns
    average_meeting_duration: float = 900.0  # seconds
    typical_participants: List[str] = field(default_factory=list)
    common_topics: List[str] = field(default_factory=list)
    
    # Adaptation metrics
    feedback_count: int = 0
    satisfaction_score: float = 0.5  # 0-1
    last_updated: float = field(default_factory=time.time)


@dataclass
class LearningConfig:
    """Configuration for learning system"""
    enable_learning: bool = True
    feedback_weight: float = 0.3  # How much feedback influences adaptations
    adaptation_threshold: int = 5  # Minimum feedback entries before adapting
    profile_save_interval: float = 300.0  # Save profiles every 5 minutes
    max_feedback_age: float = 2592000.0  # 30 days in seconds
    enable_cross_team_learning: bool = False  # Learn from other teams


class LearningSystem:
    """
    Learning and Adaptation System
    
    Collects feedback, learns team preferences, and adapts bot behavior
    to improve effectiveness over time.
    """
    
    def __init__(self, config: LearningConfig, data_dir: Optional[str] = None):
        """
        Initialize learning system
        
        Args:
            config: Learning system configuration
            data_dir: Directory to store learning data
        """
        self.config = config
        self.data_dir = Path(data_dir) if data_dir else Path("data/learning")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Feedback storage
        self.feedback_entries: List[FeedbackEntry] = []
        self.team_profiles: Dict[str, TeamProfile] = {}
        
        # Learning metrics
        self.learning_stats = {
            'total_feedback': 0,
            'adaptations_made': 0,
            'teams_learned': 0,
            'average_satisfaction': 0.0
        }
        
        # Load existing data
        self._load_data()
        
        logger.info("Learning System initialized")
    
    def collect_feedback(self, 
                        feedback_type: FeedbackType,
                        rating: FeedbackRating,
                        participant: str,
                        team_id: str,
                        context: Dict[str, Any],
                        comment: Optional[str] = None,
                        meeting_id: Optional[str] = None,
                        bot_action_id: Optional[str] = None) -> None:
        """
        Collect feedback from participants
        
        Args:
            feedback_type: Type of feedback
            rating: Rating given by participant
            participant: Name of participant giving feedback
            team_id: Team identifier
            context: Context information
            comment: Optional text comment
            meeting_id: Optional meeting identifier
            bot_action_id: Optional bot action identifier
        """
        if not self.config.enable_learning:
            return
        
        feedback = FeedbackEntry(
            feedback_type=feedback_type,
            rating=rating,
            timestamp=time.time(),
            participant=participant,
            team_id=team_id,
            context=context,
            comment=comment,
            meeting_id=meeting_id,
            bot_action_id=bot_action_id
        )
        
        self.feedback_entries.append(feedback)
        self.learning_stats['total_feedback'] += 1
        
        # Update team profile
        self._update_team_profile(team_id, feedback)
        
        # Check if adaptation is needed
        if self._should_adapt(team_id):
            self._adapt_team_preferences(team_id)
        
        logger.info(f"Feedback collected: {feedback_type.value} - {rating.value}")
    
    def get_team_adaptations(self, team_id: str) -> Dict[str, Any]:
        """
        Get current adaptations for a team
        
        Args:
            team_id: Team identifier
            
        Returns:
            Dictionary of adaptations to apply
        """
        if team_id not in self.team_profiles:
            return self._get_default_adaptations()
        
        profile = self.team_profiles[team_id]
        
        return {
            'personality': profile.preferred_personality,
            'interruption_threshold': 0.7 + (profile.interruption_sensitivity - 0.5) * 0.4,
            'suggestion_frequency_modifier': profile.suggestion_frequency,
            'technical_tolerance': profile.technical_tolerance,
            'max_meeting_duration': profile.average_meeting_duration * 1.2,
            'politeness_factor': 0.9 if profile.preferred_personality == 'friendly' else 0.7
        }
    
    def analyze_team_patterns(self, team_id: str) -> Dict[str, Any]:
        """
        Analyze patterns for a specific team
        
        Args:
            team_id: Team identifier
            
        Returns:
            Analysis of team patterns and recommendations
        """
        if team_id not in self.team_profiles:
            return {'status': 'insufficient_data', 'recommendations': []}
        
        profile = self.team_profiles[team_id]
        team_feedback = [f for f in self.feedback_entries if f.team_id == team_id]
        
        # Analyze feedback patterns
        analysis = {
            'team_id': team_id,
            'feedback_count': len(team_feedback),
            'satisfaction_score': profile.satisfaction_score,
            'preferred_personality': profile.preferred_personality,
            'common_issues': self._identify_common_issues(team_feedback),
            'improvement_areas': self._identify_improvement_areas(team_feedback),
            'recommendations': self._generate_recommendations(profile, team_feedback)
        }
        
        return analysis
    
    def get_learning_analytics(self) -> Dict[str, Any]:
        """Get analytics on learning system performance"""
        
        # Calculate satisfaction by team
        team_satisfaction = {}
        for team_id, profile in self.team_profiles.items():
            team_satisfaction[team_id] = profile.satisfaction_score
        
        # Calculate feedback distribution
        feedback_by_type = {}
        for feedback in self.feedback_entries:
            feedback_type = feedback.feedback_type.value
            feedback_by_type[feedback_type] = feedback_by_type.get(feedback_type, 0) + 1
        
        # Calculate average satisfaction
        if self.team_profiles:
            avg_satisfaction = sum(p.satisfaction_score for p in self.team_profiles.values()) / len(self.team_profiles)
        else:
            avg_satisfaction = 0.0
        
        return {
            'learning_stats': self.learning_stats.copy(),
            'team_count': len(self.team_profiles),
            'average_satisfaction': avg_satisfaction,
            'team_satisfaction': team_satisfaction,
            'feedback_distribution': feedback_by_type,
            'recent_adaptations': self._get_recent_adaptations()
        }
    
    def _update_team_profile(self, team_id: str, feedback: FeedbackEntry):
        """Update team profile based on feedback"""
        
        if team_id not in self.team_profiles:
            self.team_profiles[team_id] = TeamProfile(
                team_id=team_id,
                team_name=f"Team {team_id}"
            )
        
        profile = self.team_profiles[team_id]
        profile.feedback_count += 1
        profile.last_updated = time.time()
        
        # Update satisfaction score (weighted average)
        rating_value = (feedback.rating.value + 2) / 4  # Convert -2,2 to 0,1
        weight = 0.1  # How much new feedback influences score
        profile.satisfaction_score = (
            profile.satisfaction_score * (1 - weight) + rating_value * weight
        )
        
        # Update participant list
        if feedback.participant not in profile.typical_participants:
            profile.typical_participants.append(feedback.participant)
        
        # Learn from specific feedback types
        if feedback.feedback_type == FeedbackType.PERSONALITY_FEEDBACK:
            self._learn_personality_preference(profile, feedback)
        elif feedback.feedback_type == FeedbackType.INTERRUPTION_FEEDBACK:
            self._learn_interruption_preference(profile, feedback)
        elif feedback.feedback_type == FeedbackType.SUGGESTION_FEEDBACK:
            self._learn_suggestion_preference(profile, feedback)
    
    def _learn_personality_preference(self, profile: TeamProfile, feedback: FeedbackEntry):
        """Learn personality preferences from feedback"""
        
        current_personality = feedback.context.get('personality', 'professional')
        
        if feedback.rating.value >= 1:  # Positive feedback
            profile.preferred_personality = current_personality
        elif feedback.rating.value <= -1:  # Negative feedback
            # Try different personality
            personalities = ['professional', 'friendly', 'assertive']
            personalities.remove(current_personality)
            profile.preferred_personality = personalities[0]  # Try first alternative
    
    def _learn_interruption_preference(self, profile: TeamProfile, feedback: FeedbackEntry):
        """Learn interruption sensitivity from feedback"""
        
        if feedback.rating.value >= 1:  # Positive feedback on interruption
            # Team likes interruptions, increase sensitivity
            profile.interruption_sensitivity = min(1.0, profile.interruption_sensitivity + 0.1)
        elif feedback.rating.value <= -1:  # Negative feedback on interruption
            # Team dislikes interruptions, decrease sensitivity
            profile.interruption_sensitivity = max(0.0, profile.interruption_sensitivity - 0.1)
    
    def _learn_suggestion_preference(self, profile: TeamProfile, feedback: FeedbackEntry):
        """Learn suggestion frequency preferences from feedback"""
        
        if feedback.rating.value >= 1:  # Positive feedback on suggestion
            # Team likes suggestions, increase frequency
            profile.suggestion_frequency = min(1.0, profile.suggestion_frequency + 0.1)
        elif feedback.rating.value <= -1:  # Negative feedback on suggestion
            # Team dislikes suggestions, decrease frequency
            profile.suggestion_frequency = max(0.0, profile.suggestion_frequency - 0.1)
    
    def _should_adapt(self, team_id: str) -> bool:
        """Check if team profile should be adapted"""
        
        if team_id not in self.team_profiles:
            return False
        
        profile = self.team_profiles[team_id]
        return profile.feedback_count >= self.config.adaptation_threshold
    
    def _adapt_team_preferences(self, team_id: str):
        """Adapt team preferences based on accumulated feedback"""
        
        if team_id not in self.team_profiles:
            return
        
        profile = self.team_profiles[team_id]
        team_feedback = [f for f in self.feedback_entries if f.team_id == team_id]
        
        # Analyze recent feedback (last 30 days)
        recent_cutoff = time.time() - self.config.max_feedback_age
        recent_feedback = [f for f in team_feedback if f.timestamp > recent_cutoff]
        
        if not recent_feedback:
            return
        
        # Calculate adaptation adjustments
        adaptations_made = 0
        
        # Adapt based on overall satisfaction
        avg_rating = sum(f.rating.value for f in recent_feedback) / len(recent_feedback)
        
        if avg_rating < -0.5:  # Generally negative feedback
            # Make bot less intrusive
            profile.interruption_sensitivity = max(0.3, profile.interruption_sensitivity - 0.2)
            profile.suggestion_frequency = max(0.2, profile.suggestion_frequency - 0.2)
            adaptations_made += 1
        elif avg_rating > 0.5:  # Generally positive feedback
            # Bot can be more active
            profile.interruption_sensitivity = min(0.9, profile.interruption_sensitivity + 0.1)
            profile.suggestion_frequency = min(0.8, profile.suggestion_frequency + 0.1)
            adaptations_made += 1
        
        if adaptations_made > 0:
            self.learning_stats['adaptations_made'] += adaptations_made
            logger.info(f"Adapted preferences for team {team_id}: {adaptations_made} changes")
    
    def _identify_common_issues(self, team_feedback: List[FeedbackEntry]) -> List[str]:
        """Identify common issues from feedback"""
        
        issues = []
        negative_feedback = [f for f in team_feedback if f.rating.value <= -1]
        
        # Count feedback types with negative ratings
        issue_counts = {}
        for feedback in negative_feedback:
            issue_type = feedback.feedback_type.value
            issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1
        
        # Identify issues that appear frequently
        total_negative = len(negative_feedback)
        if total_negative > 0:
            for issue_type, count in issue_counts.items():
                if count / total_negative > 0.3:  # More than 30% of negative feedback
                    issues.append(f"Frequent negative feedback on {issue_type}")
        
        return issues
    
    def _identify_improvement_areas(self, team_feedback: List[FeedbackEntry]) -> List[str]:
        """Identify areas for improvement"""
        
        improvements = []
        
        # Analyze feedback patterns
        if len(team_feedback) < 5:
            improvements.append("Need more feedback to identify patterns")
            return improvements
        
        # Check satisfaction trend
        recent_feedback = sorted(team_feedback, key=lambda f: f.timestamp)[-5:]
        recent_ratings = [f.rating.value for f in recent_feedback]
        avg_recent = sum(recent_ratings) / len(recent_ratings)
        
        if avg_recent < 0:
            improvements.append("Overall satisfaction is declining")
        
        # Check specific areas
        interruption_feedback = [f for f in team_feedback if f.feedback_type == FeedbackType.INTERRUPTION_FEEDBACK]
        if interruption_feedback:
            avg_interruption_rating = sum(f.rating.value for f in interruption_feedback) / len(interruption_feedback)
            if avg_interruption_rating < -0.5:
                improvements.append("Interruption timing and frequency need adjustment")
        
        suggestion_feedback = [f for f in team_feedback if f.feedback_type == FeedbackType.SUGGESTION_FEEDBACK]
        if suggestion_feedback:
            avg_suggestion_rating = sum(f.rating.value for f in suggestion_feedback) / len(suggestion_feedback)
            if avg_suggestion_rating < -0.5:
                improvements.append("Suggestion relevance and timing need improvement")
        
        return improvements
    
    def _generate_recommendations(self, profile: TeamProfile, team_feedback: List[FeedbackEntry]) -> List[str]:
        """Generate recommendations for team"""
        
        recommendations = []
        
        # Based on satisfaction score
        if profile.satisfaction_score < 0.4:
            recommendations.append("Consider adjusting bot personality or reducing intervention frequency")
        elif profile.satisfaction_score > 0.8:
            recommendations.append("Team is highly satisfied - current settings are working well")
        
        # Based on feedback patterns
        if profile.interruption_sensitivity < 0.3:
            recommendations.append("Team prefers minimal interruptions - focus on end-of-meeting summaries")
        elif profile.interruption_sensitivity > 0.8:
            recommendations.append("Team appreciates active moderation - continue current approach")
        
        if profile.suggestion_frequency < 0.3:
            recommendations.append("Reduce suggestion frequency - team prefers autonomous meetings")
        elif profile.suggestion_frequency > 0.8:
            recommendations.append("Team values suggestions - consider adding more contextual guidance")
        
        return recommendations
    
    def _get_default_adaptations(self) -> Dict[str, Any]:
        """Get default adaptations for new teams"""
        
        return {
            'personality': 'professional',
            'interruption_threshold': 0.7,
            'suggestion_frequency_modifier': 0.5,
            'technical_tolerance': 0.5,
            'max_meeting_duration': 900.0,
            'politeness_factor': 0.8
        }
    
    def _get_recent_adaptations(self) -> List[Dict[str, Any]]:
        """Get recent adaptations made"""
        
        recent_adaptations = []
        recent_cutoff = time.time() - 86400  # Last 24 hours
        
        for team_id, profile in self.team_profiles.items():
            if profile.last_updated > recent_cutoff:
                recent_adaptations.append({
                    'team_id': team_id,
                    'timestamp': profile.last_updated,
                    'satisfaction_score': profile.satisfaction_score,
                    'adaptations': {
                        'personality': profile.preferred_personality,
                        'interruption_sensitivity': profile.interruption_sensitivity,
                        'suggestion_frequency': profile.suggestion_frequency
                    }
                })
        
        return sorted(recent_adaptations, key=lambda x: x['timestamp'], reverse=True)
    
    def _load_data(self):
        """Load existing learning data"""
        
        try:
            # Load team profiles
            profiles_file = self.data_dir / "team_profiles.json"
            if profiles_file.exists():
                with open(profiles_file, 'r') as f:
                    profiles_data = json.load(f)
                
                for team_id, profile_data in profiles_data.items():
                    self.team_profiles[team_id] = TeamProfile(**profile_data)
                
                logger.info(f"Loaded {len(self.team_profiles)} team profiles")
            
            # Load feedback entries (recent only)
            feedback_file = self.data_dir / "feedback_entries.json"
            if feedback_file.exists():
                with open(feedback_file, 'r') as f:
                    feedback_data = json.load(f)
                
                recent_cutoff = time.time() - self.config.max_feedback_age
                for entry_data in feedback_data:
                    if entry_data['timestamp'] > recent_cutoff:
                        feedback = FeedbackEntry(
                            feedback_type=FeedbackType(entry_data['feedback_type']),
                            rating=FeedbackRating(entry_data['rating']),
                            timestamp=entry_data['timestamp'],
                            participant=entry_data['participant'],
                            team_id=entry_data['team_id'],
                            context=entry_data['context'],
                            comment=entry_data.get('comment'),
                            meeting_id=entry_data.get('meeting_id'),
                            bot_action_id=entry_data.get('bot_action_id')
                        )
                        self.feedback_entries.append(feedback)
                
                logger.info(f"Loaded {len(self.feedback_entries)} recent feedback entries")
        
        except Exception as e:
            logger.warning(f"Failed to load learning data: {e}")
    
    def save_data(self):
        """Save learning data to disk"""
        
        try:
            # Save team profiles
            profiles_data = {}
            for team_id, profile in self.team_profiles.items():
                profiles_data[team_id] = {
                    'team_id': profile.team_id,
                    'team_name': profile.team_name,
                    'preferred_personality': profile.preferred_personality,
                    'interruption_sensitivity': profile.interruption_sensitivity,
                    'suggestion_frequency': profile.suggestion_frequency,
                    'technical_tolerance': profile.technical_tolerance,
                    'average_meeting_duration': profile.average_meeting_duration,
                    'typical_participants': profile.typical_participants,
                    'common_topics': profile.common_topics,
                    'feedback_count': profile.feedback_count,
                    'satisfaction_score': profile.satisfaction_score,
                    'last_updated': profile.last_updated
                }
            
            profiles_file = self.data_dir / "team_profiles.json"
            with open(profiles_file, 'w') as f:
                json.dump(profiles_data, f, indent=2)
            
            # Save recent feedback entries
            feedback_data = []
            recent_cutoff = time.time() - self.config.max_feedback_age
            
            for feedback in self.feedback_entries:
                if feedback.timestamp > recent_cutoff:
                    feedback_data.append({
                        'feedback_type': feedback.feedback_type.value,
                        'rating': feedback.rating.value,
                        'timestamp': feedback.timestamp,
                        'participant': feedback.participant,
                        'team_id': feedback.team_id,
                        'context': feedback.context,
                        'comment': feedback.comment,
                        'meeting_id': feedback.meeting_id,
                        'bot_action_id': feedback.bot_action_id
                    })
            
            feedback_file = self.data_dir / "feedback_entries.json"
            with open(feedback_file, 'w') as f:
                json.dump(feedback_data, f, indent=2)
            
            logger.info("Learning data saved successfully")
        
        except Exception as e:
            logger.error(f"Failed to save learning data: {e}")
    
    def cleanup_old_data(self):
        """Clean up old feedback data"""
        
        cutoff_time = time.time() - self.config.max_feedback_age
        
        # Remove old feedback entries
        self.feedback_entries = [
            f for f in self.feedback_entries 
            if f.timestamp > cutoff_time
        ]
        
        logger.info("Old feedback data cleaned up")
