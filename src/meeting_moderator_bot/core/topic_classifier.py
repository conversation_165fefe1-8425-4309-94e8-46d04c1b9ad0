"""
Topic Classifier - Classifies conversation topics for meeting moderation

Uses AI-powered analysis to determine if conversation topics are appropriate
for standup meetings or if they represent off-topic discussions.
"""

import logging
import re
import json
import time
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum

import google.generativeai as genai

logger = logging.getLogger(__name__)


class TopicType(Enum):
    """Types of conversation topics"""
    STANDUP_UPDATE = "standup_update"
    TECHNICAL_DEEP_DIVE = "technical_deep_dive"
    OFF_TOPIC_DISCUSSION = "off_topic_discussion"
    PROBLEM_SOLVING = "problem_solving"
    BLOCKER_DISCUSSION = "blocker_discussion"
    QUESTION_CLARIFICATION = "question_clarification"


@dataclass
class IntentAnalysis:
    """Result of intent analysis"""
    primary_intent: str  # 'update', 'question', 'blocker', 'discussion', 'off_topic'
    confidence: float
    entities: List[str]  # Extracted entities (projects, people, technologies)
    sentiment: str  # 'positive', 'negative', 'neutral', 'frustrated'
    urgency_level: float  # 0-1, how urgent the topic seems
    context_relevance: float  # 0-1, how relevant to current meeting context


@dataclass
class SemanticAnalysis:
    """Result of semantic analysis"""
    main_topics: List[str]
    related_concepts: List[str]
    complexity_score: float  # 0-1, how complex/technical the content is
    coherence_score: float  # 0-1, how coherent the message is
    summary: str  # Brief summary of the content


@dataclass
class TopicClassification:
    """Enhanced result of topic classification"""
    topic_type: str
    is_standup_relevant: bool
    confidence: float
    keywords: List[str]
    technical_indicators: List[str]
    reasoning: str

    # Enhanced NLP fields
    intent_analysis: Optional[IntentAnalysis] = None
    semantic_analysis: Optional[SemanticAnalysis] = None
    conversation_flow_score: float = 0.0  # How well this fits conversation flow
    interruption_risk: float = 0.0  # Risk that this needs interruption


@dataclass
class ClassificationConfig:
    """Configuration for topic classification"""
    confidence_threshold: float = 0.7
    technical_keywords_weight: float = 0.3
    context_window_size: int = 5
    use_ai_classification: bool = True
    gemini_model: str = "gemini-2.5-flash"


class TopicClassifier:
    """
    Classifies conversation topics to determine standup relevance
    
    Uses both rule-based and AI-powered classification to identify
    when conversations drift from standup format.
    """
    
    def __init__(self, gemini_api_key: str, config: ClassificationConfig):
        """
        Initialize topic classifier
        
        Args:
            gemini_api_key: API key for Gemini AI
            config: Classification configuration
        """
        self.config = config
        self.gemini_api_key = gemini_api_key
        
        # Initialize Gemini if AI classification is enabled
        if config.use_ai_classification:
            genai.configure(api_key=gemini_api_key)
            self.model = genai.GenerativeModel(config.gemini_model)
        else:
            self.model = None
        
        # Define keyword patterns for rule-based classification
        self._setup_keyword_patterns()
        
        logger.info("Topic Classifier initialized")
    
    def _setup_keyword_patterns(self):
        """Setup keyword patterns for classification"""
        
        # Standup-relevant keywords
        self.standup_keywords = {
            'yesterday', 'today', 'tomorrow', 'working on', 'completed', 'finished',
            'blocked', 'blocker', 'impediment', 'help', 'stuck', 'issue',
            'progress', 'update', 'status', 'done', 'doing', 'will do',
            'planning', 'scheduled', 'meeting', 'review', 'testing'
        }
        
        # Technical deep-dive indicators
        self.technical_keywords = {
            'algorithm', 'implementation', 'architecture', 'database', 'query',
            'optimization', 'performance', 'memory', 'cpu', 'latency',
            'framework', 'library', 'dependency', 'version', 'configuration',
            'deployment', 'infrastructure', 'server', 'network', 'security',
            'authentication', 'authorization', 'encryption', 'protocol',
            'api', 'endpoint', 'request', 'response', 'json', 'xml',
            'debugging', 'logging', 'monitoring', 'metrics', 'analytics'
        }
        
        # Off-topic indicators
        self.off_topic_keywords = {
            'weekend', 'vacation', 'holiday', 'weather', 'lunch', 'coffee',
            'movie', 'tv', 'sports', 'game', 'family', 'personal',
            'politics', 'news', 'gossip', 'rumor', 'joke', 'funny'
        }
        
        # Problem-solving indicators
        self.problem_solving_keywords = {
            'solution', 'approach', 'strategy', 'design', 'architecture',
            'brainstorm', 'idea', 'suggestion', 'proposal', 'alternative',
            'option', 'choice', 'decision', 'analysis', 'evaluation'
        }
    
    def classify_text(self, text: str, meeting_phase: str, 
                     context: Optional[str] = None) -> TopicClassification:
        """
        Classify a piece of text for standup relevance
        
        Args:
            text: Text to classify
            meeting_phase: Current meeting phase
            context: Additional context from conversation
            
        Returns:
            TopicClassification result
        """
        # Normalize text
        normalized_text = text.lower().strip()
        
        # Extract keywords
        keywords = self._extract_keywords(normalized_text)
        technical_indicators = self._extract_technical_indicators(normalized_text)
        
        # Rule-based classification
        rule_based_result = self._rule_based_classification(
            normalized_text, keywords, technical_indicators, meeting_phase
        )
        
        # AI-powered classification (if enabled)
        if self.config.use_ai_classification and self.model:
            ai_result = self._ai_classification(text, meeting_phase, context)

            # Enhanced NLP analysis
            intent_analysis = self._analyze_intent(text, meeting_phase, context)
            semantic_analysis = self._analyze_semantics(text, context)

            # Combine rule-based and AI results with enhanced analysis
            final_result = self._combine_classifications(rule_based_result, ai_result)
            final_result.intent_analysis = intent_analysis
            final_result.semantic_analysis = semantic_analysis
            final_result.conversation_flow_score = self._calculate_flow_score(text, context)
            final_result.interruption_risk = self._calculate_interruption_risk(final_result)
        else:
            final_result = rule_based_result
        
        logger.debug(f"Classified text as {final_result.topic_type} "
                    f"(confidence: {final_result.confidence:.2f})")
        
        return final_result
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from text"""
        words = re.findall(r'\b\w+\b', text.lower())
        
        found_keywords = []
        all_keywords = (self.standup_keywords | self.technical_keywords | 
                       self.off_topic_keywords | self.problem_solving_keywords)
        
        for word in words:
            if word in all_keywords:
                found_keywords.append(word)
        
        return found_keywords
    
    def _extract_technical_indicators(self, text: str) -> List[str]:
        """Extract technical indicators from text"""
        words = re.findall(r'\b\w+\b', text.lower())
        
        technical_indicators = []
        for word in words:
            if word in self.technical_keywords:
                technical_indicators.append(word)
        
        # Look for technical patterns
        technical_patterns = [
            r'\b\d+\.\d+\.\d+\b',  # Version numbers
            r'\b[A-Z]{2,}\b',      # Acronyms
            r'\b\w+\(\)\b',        # Function calls
            r'\b\w+\.\w+\b',       # Method calls or file extensions
        ]
        
        for pattern in technical_patterns:
            matches = re.findall(pattern, text)
            technical_indicators.extend(matches)
        
        return technical_indicators
    
    def _rule_based_classification(self, text: str, keywords: List[str],
                                 technical_indicators: List[str],
                                 meeting_phase: str) -> TopicClassification:
        """Perform rule-based classification"""
        
        # Count keyword types
        standup_count = sum(1 for kw in keywords if kw in self.standup_keywords)
        technical_count = sum(1 for kw in keywords if kw in self.technical_keywords)
        off_topic_count = sum(1 for kw in keywords if kw in self.off_topic_keywords)
        problem_solving_count = sum(1 for kw in keywords if kw in self.problem_solving_keywords)
        
        # Calculate scores
        total_words = len(text.split())
        standup_score = standup_count / max(total_words, 1)
        technical_score = (technical_count + len(technical_indicators)) / max(total_words, 1)
        off_topic_score = off_topic_count / max(total_words, 1)
        problem_solving_score = problem_solving_count / max(total_words, 1)
        
        # Determine topic type and confidence
        if off_topic_score > 0.1:
            topic_type = TopicType.OFF_TOPIC_DISCUSSION.value
            confidence = min(0.9, off_topic_score * 5)
            is_relevant = False
            reasoning = f"High off-topic keyword density ({off_topic_score:.2f})"
            
        elif technical_score > 0.15 and len(text.split()) > 20:
            topic_type = TopicType.TECHNICAL_DEEP_DIVE.value
            confidence = min(0.9, technical_score * 3)
            is_relevant = False
            reasoning = f"High technical content density ({technical_score:.2f}) in long message"
            
        elif problem_solving_score > 0.1 and len(text.split()) > 15:
            topic_type = TopicType.PROBLEM_SOLVING.value
            confidence = min(0.8, problem_solving_score * 4)
            is_relevant = False
            reasoning = f"Problem-solving discussion detected ({problem_solving_score:.2f})"
            
        elif standup_score > 0.05 or any(kw in text for kw in ['yesterday', 'today', 'blocked', 'working']):
            topic_type = TopicType.STANDUP_UPDATE.value
            confidence = min(0.9, standup_score * 10 + 0.3)
            is_relevant = True
            reasoning = f"Contains standup-relevant keywords ({standup_score:.2f})"
            
        else:
            # Default classification based on length and context
            if len(text.split()) > 30:
                topic_type = TopicType.TECHNICAL_DEEP_DIVE.value
                confidence = 0.6
                is_relevant = False
                reasoning = "Long message without clear standup indicators"
            else:
                topic_type = TopicType.STANDUP_UPDATE.value
                confidence = 0.5
                is_relevant = True
                reasoning = "Short message, assumed standup-relevant"
        
        return TopicClassification(
            topic_type=topic_type,
            is_standup_relevant=is_relevant,
            confidence=confidence,
            keywords=keywords,
            technical_indicators=technical_indicators,
            reasoning=reasoning
        )
    
    def _ai_classification(self, text: str, meeting_phase: str,
                          context: Optional[str] = None) -> TopicClassification:
        """Perform AI-powered classification using Gemini"""
        
        try:
            # Build prompt for classification
            prompt = self._build_classification_prompt(text, meeting_phase, context)
            
            # Generate classification
            response = self.model.generate_content(prompt)
            
            # Parse response
            return self._parse_ai_response(response.text, text)
            
        except Exception as e:
            logger.warning(f"AI classification failed: {e}")
            # Fallback to rule-based classification
            return TopicClassification(
                topic_type=TopicType.STANDUP_UPDATE.value,
                is_standup_relevant=True,
                confidence=0.5,
                keywords=[],
                technical_indicators=[],
                reasoning="AI classification failed, using fallback"
            )
    
    def _build_classification_prompt(self, text: str, meeting_phase: str,
                                   context: Optional[str] = None) -> str:
        """Build prompt for AI classification"""
        
        prompt = f"""Analyze this text from a daily standup meeting and classify it:

Text: "{text}"
Meeting Phase: {meeting_phase}
Context: {context or "None"}

Classify this text into one of these categories:
1. standup_update - Appropriate standup content (what did, what will do, blockers)
2. technical_deep_dive - Technical details that should be discussed separately
3. off_topic_discussion - Unrelated to work or standup
4. problem_solving - Detailed problem-solving that needs separate meeting
5. blocker_discussion - Discussion about blockers (appropriate for standup)
6. question_clarification - Quick clarifying questions (appropriate for standup)

Respond in this exact format:
TOPIC_TYPE: [category]
CONFIDENCE: [0.0-1.0]
IS_STANDUP_RELEVANT: [true/false]
REASONING: [brief explanation]

Focus on whether this content belongs in a quick daily standup or needs separate discussion."""
        
        return prompt
    
    def _parse_ai_response(self, response: str, original_text: str) -> TopicClassification:
        """Parse AI response into TopicClassification"""
        
        try:
            lines = response.strip().split('\n')
            
            topic_type = TopicType.STANDUP_UPDATE.value
            confidence = 0.5
            is_relevant = True
            reasoning = "Default classification"
            
            for line in lines:
                if line.startswith('TOPIC_TYPE:'):
                    topic_type = line.split(':', 1)[1].strip()
                elif line.startswith('CONFIDENCE:'):
                    confidence = float(line.split(':', 1)[1].strip())
                elif line.startswith('IS_STANDUP_RELEVANT:'):
                    is_relevant = line.split(':', 1)[1].strip().lower() == 'true'
                elif line.startswith('REASONING:'):
                    reasoning = line.split(':', 1)[1].strip()
            
            return TopicClassification(
                topic_type=topic_type,
                is_standup_relevant=is_relevant,
                confidence=confidence,
                keywords=self._extract_keywords(original_text),
                technical_indicators=self._extract_technical_indicators(original_text),
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.warning(f"Failed to parse AI response: {e}")
            return TopicClassification(
                topic_type=TopicType.STANDUP_UPDATE.value,
                is_standup_relevant=True,
                confidence=0.5,
                keywords=[],
                technical_indicators=[],
                reasoning="Failed to parse AI response"
            )
    
    def _combine_classifications(self, rule_based: TopicClassification,
                               ai_based: TopicClassification) -> TopicClassification:
        """Combine rule-based and AI classifications"""
        
        # Weight the classifications
        rule_weight = 0.4
        ai_weight = 0.6
        
        # Combine confidence scores
        combined_confidence = (rule_based.confidence * rule_weight + 
                             ai_based.confidence * ai_weight)
        
        # Choose topic type based on higher confidence
        if rule_based.confidence > ai_based.confidence:
            topic_type = rule_based.topic_type
            is_relevant = rule_based.is_standup_relevant
            reasoning = f"Rule-based: {rule_based.reasoning}; AI: {ai_based.reasoning}"
        else:
            topic_type = ai_based.topic_type
            is_relevant = ai_based.is_standup_relevant
            reasoning = f"AI: {ai_based.reasoning}; Rule-based: {rule_based.reasoning}"
        
        return TopicClassification(
            topic_type=topic_type,
            is_standup_relevant=is_relevant,
            confidence=combined_confidence,
            keywords=rule_based.keywords,
            technical_indicators=rule_based.technical_indicators,
            reasoning=reasoning
        )

    def _analyze_intent(self, text: str, meeting_phase: str,
                       context: Optional[str] = None) -> IntentAnalysis:
        """Analyze the intent behind the text using advanced NLP"""

        try:
            # Build intent analysis prompt
            prompt = f"""Analyze the intent and extract information from this standup meeting text:

Text: "{text}"
Meeting Phase: {meeting_phase}
Context: {context or "None"}

Analyze and respond in this JSON format:
{{
    "primary_intent": "update|question|blocker|discussion|off_topic",
    "confidence": 0.0-1.0,
    "entities": ["list", "of", "extracted", "entities"],
    "sentiment": "positive|negative|neutral|frustrated",
    "urgency_level": 0.0-1.0,
    "context_relevance": 0.0-1.0
}}

Focus on:
- What is the speaker trying to accomplish?
- What entities (projects, people, technologies) are mentioned?
- What is the emotional tone?
- How urgent does this seem?
- How relevant is this to a standup meeting?"""

            response = self.model.generate_content(prompt)

            # Parse JSON response
            try:
                result = json.loads(response.text.strip())

                return IntentAnalysis(
                    primary_intent=result.get('primary_intent', 'update'),
                    confidence=float(result.get('confidence', 0.5)),
                    entities=result.get('entities', []),
                    sentiment=result.get('sentiment', 'neutral'),
                    urgency_level=float(result.get('urgency_level', 0.0)),
                    context_relevance=float(result.get('context_relevance', 0.5))
                )

            except (json.JSONDecodeError, KeyError, ValueError) as e:
                logger.warning(f"Failed to parse intent analysis JSON: {e}")
                return self._fallback_intent_analysis(text)

        except Exception as e:
            logger.warning(f"Intent analysis failed: {e}")
            return self._fallback_intent_analysis(text)

    def _analyze_semantics(self, text: str, context: Optional[str] = None) -> SemanticAnalysis:
        """Perform semantic analysis of the text"""

        try:
            # Build semantic analysis prompt
            prompt = f"""Perform semantic analysis of this standup meeting text:

Text: "{text}"
Context: {context or "None"}

Analyze and respond in this JSON format:
{{
    "main_topics": ["list", "of", "main", "topics"],
    "related_concepts": ["related", "concepts", "mentioned"],
    "complexity_score": 0.0-1.0,
    "coherence_score": 0.0-1.0,
    "summary": "Brief summary of the content"
}}

Focus on:
- What are the main topics discussed?
- What related concepts are mentioned?
- How complex/technical is the content? (0=simple, 1=very technical)
- How coherent and well-structured is the message? (0=rambling, 1=clear)
- Provide a brief summary"""

            response = self.model.generate_content(prompt)

            # Parse JSON response
            try:
                result = json.loads(response.text.strip())

                return SemanticAnalysis(
                    main_topics=result.get('main_topics', []),
                    related_concepts=result.get('related_concepts', []),
                    complexity_score=float(result.get('complexity_score', 0.5)),
                    coherence_score=float(result.get('coherence_score', 0.5)),
                    summary=result.get('summary', text[:100] + "...")
                )

            except (json.JSONDecodeError, KeyError, ValueError) as e:
                logger.warning(f"Failed to parse semantic analysis JSON: {e}")
                return self._fallback_semantic_analysis(text)

        except Exception as e:
            logger.warning(f"Semantic analysis failed: {e}")
            return self._fallback_semantic_analysis(text)

    def _calculate_flow_score(self, text: str, context: Optional[str] = None) -> float:
        """Calculate how well this text fits the conversation flow"""

        if not context:
            return 0.5  # Neutral score without context

        # Simple heuristics for conversation flow
        text_lower = text.lower()
        context_lower = context.lower() if context else ""

        flow_score = 0.5  # Base score

        # Check for conversation continuity indicators
        continuity_indicators = [
            'also', 'additionally', 'furthermore', 'moreover', 'similarly',
            'regarding', 'about that', 'speaking of', 'related to'
        ]

        for indicator in continuity_indicators:
            if indicator in text_lower:
                flow_score += 0.1

        # Check for topic shifts
        shift_indicators = [
            'by the way', 'changing topics', 'different subject', 'unrelated',
            'off topic', 'random question'
        ]

        for indicator in shift_indicators:
            if indicator in text_lower:
                flow_score -= 0.2

        # Check for references to previous context
        if context_lower:
            # Simple word overlap check
            text_words = set(text_lower.split())
            context_words = set(context_lower.split())
            overlap = len(text_words & context_words)
            total_words = len(text_words)

            if total_words > 0:
                overlap_ratio = overlap / total_words
                flow_score += overlap_ratio * 0.3

        return max(0.0, min(1.0, flow_score))

    def _calculate_interruption_risk(self, classification: TopicClassification) -> float:
        """Calculate the risk that this content needs interruption"""

        risk_score = 0.0

        # Base risk from topic type
        if not classification.is_standup_relevant:
            risk_score += 0.6

        if classification.topic_type == 'technical_deep_dive':
            risk_score += 0.4
        elif classification.topic_type == 'problem_solving':
            risk_score += 0.5
        elif classification.topic_type == 'off_topic_discussion':
            risk_score += 0.7

        # Risk from intent analysis
        if classification.intent_analysis:
            intent = classification.intent_analysis

            # High urgency but low relevance = risk
            if intent.urgency_level > 0.7 and intent.context_relevance < 0.3:
                risk_score += 0.3

            # Frustrated sentiment = higher risk
            if intent.sentiment == 'frustrated':
                risk_score += 0.2

            # Discussion intent in standup = risk
            if intent.primary_intent == 'discussion':
                risk_score += 0.3

        # Risk from semantic analysis
        if classification.semantic_analysis:
            semantic = classification.semantic_analysis

            # High complexity = risk
            if semantic.complexity_score > 0.7:
                risk_score += 0.2

            # Low coherence = risk (rambling)
            if semantic.coherence_score < 0.3:
                risk_score += 0.3

        # Risk from conversation flow
        if classification.conversation_flow_score < 0.3:
            risk_score += 0.2

        return max(0.0, min(1.0, risk_score))

    def _fallback_intent_analysis(self, text: str) -> IntentAnalysis:
        """Fallback intent analysis using simple rules"""

        text_lower = text.lower()

        # Determine primary intent
        if any(word in text_lower for word in ['yesterday', 'today', 'working on', 'completed']):
            primary_intent = 'update'
        elif any(word in text_lower for word in ['?', 'how', 'what', 'why', 'when', 'where']):
            primary_intent = 'question'
        elif any(word in text_lower for word in ['blocked', 'stuck', 'issue', 'problem']):
            primary_intent = 'blocker'
        elif any(word in text_lower for word in ['think', 'should', 'could', 'maybe']):
            primary_intent = 'discussion'
        else:
            primary_intent = 'off_topic'

        # Simple sentiment analysis
        positive_words = ['good', 'great', 'excellent', 'completed', 'finished', 'done']
        negative_words = ['problem', 'issue', 'stuck', 'blocked', 'difficult', 'hard']

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            sentiment = 'positive'
        elif negative_count > positive_count:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'

        return IntentAnalysis(
            primary_intent=primary_intent,
            confidence=0.6,  # Lower confidence for fallback
            entities=[],
            sentiment=sentiment,
            urgency_level=0.5,
            context_relevance=0.5
        )

    def _fallback_semantic_analysis(self, text: str) -> SemanticAnalysis:
        """Fallback semantic analysis using simple rules"""

        # Extract potential topics (nouns and technical terms)
        words = text.lower().split()

        # Simple topic extraction
        main_topics = []
        for word in words:
            if word in self.technical_keywords or word in self.standup_keywords:
                main_topics.append(word)

        # Calculate complexity based on technical keywords
        technical_word_count = sum(1 for word in words if word in self.technical_keywords)
        complexity_score = min(1.0, technical_word_count / max(len(words), 1) * 5)

        # Calculate coherence based on sentence structure
        sentence_count = text.count('.') + text.count('!') + text.count('?') + 1
        avg_sentence_length = len(words) / sentence_count
        coherence_score = max(0.0, min(1.0, 1.0 - abs(avg_sentence_length - 15) / 15))

        return SemanticAnalysis(
            main_topics=main_topics[:5],  # Top 5 topics
            related_concepts=[],
            complexity_score=complexity_score,
            coherence_score=coherence_score,
            summary=text[:100] + "..." if len(text) > 100 else text
        )
