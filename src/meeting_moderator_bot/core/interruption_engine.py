"""
Interruption Engine - Manages when and how to interrupt conversations

Determines appropriate timing and methods for interrupting off-topic
discussions while maintaining respectful and professional tone.
"""

import logging
import time
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

from .topic_classifier import TopicClassification
from .conversation_monitor import ConversationState

logger = logging.getLogger(__name__)


class InterruptionLevel(Enum):
    """Levels of interruption intensity"""
    GENTLE_HINT = "gentle_hint"
    POLITE_REDIRECT = "polite_redirect"
    FIRM_REDIRECT = "firm_redirect"
    ASSERTIVE_STOP = "assertive_stop"


@dataclass
class InterruptionDecision:
    """Decision about whether to interrupt"""
    should_interrupt: bool
    confidence: float
    level: InterruptionLevel
    reason: str
    suggested_message: str
    wait_time: float = 0.0  # Seconds to wait before interrupting


@dataclass
class InterruptionConfig:
    """Configuration for interruption behavior"""
    confidence_threshold: float = 0.7
    max_off_topic_duration: float = 30.0  # seconds
    max_technical_duration: float = 45.0  # seconds
    escalation_time: float = 15.0  # seconds between escalation levels
    participant_speaking_limit: float = 120.0  # 2 minutes max per participant
    politeness_factor: float = 0.8  # How polite to be (0-1)


class InterruptionEngine:
    """
    Manages interruption decisions and timing
    
    Analyzes conversation flow and determines when and how to
    interrupt off-topic or overly long discussions.
    """
    
    def __init__(self, config: InterruptionConfig):
        """
        Initialize interruption engine
        
        Args:
            config: Interruption configuration
        """
        self.config = config
        
        # Track interruption history
        self.interruption_history = []
        self.participant_interruptions = {}
        
        # State tracking
        self.current_off_topic_start = None
        self.last_interruption_time = None
        
        logger.info("Interruption Engine initialized")
    
    def should_interrupt(self, classification: TopicClassification,
                        conversation_state: ConversationState,
                        meeting_phase: str) -> InterruptionDecision:
        """
        Determine if an interruption is needed
        
        Args:
            classification: Topic classification result
            conversation_state: Current conversation state
            meeting_phase: Current meeting phase
            
        Returns:
            InterruptionDecision with recommendation
        """
        current_time = time.time()
        
        # Don't interrupt if we just interrupted recently
        if (self.last_interruption_time and 
            current_time - self.last_interruption_time < 10.0):
            return InterruptionDecision(
                should_interrupt=False,
                confidence=0.0,
                level=InterruptionLevel.GENTLE_HINT,
                reason="Recent interruption cooldown",
                suggested_message=""
            )
        
        # Check various interruption triggers
        decision = self._evaluate_interruption_triggers(
            classification, conversation_state, meeting_phase, current_time
        )
        
        if decision.should_interrupt:
            self._record_interruption(decision, current_time)
        
        return decision
    
    def _evaluate_interruption_triggers(self, classification: TopicClassification,
                                      conversation_state: ConversationState,
                                      meeting_phase: str,
                                      current_time: float) -> InterruptionDecision:
        """Evaluate all possible interruption triggers"""
        
        # 1. Off-topic content
        if not classification.is_standup_relevant:
            return self._handle_off_topic_content(
                classification, conversation_state, current_time
            )
        
        # 2. Technical deep dive
        if classification.topic_type == "technical_deep_dive":
            return self._handle_technical_deep_dive(
                classification, conversation_state, current_time
            )
        
        # 3. Excessive speaking time
        if conversation_state.current_speaker_duration > self.config.participant_speaking_limit:
            return self._handle_excessive_speaking_time(
                conversation_state, current_time
            )
        
        # 4. Problem-solving session
        if classification.topic_type == "problem_solving":
            return self._handle_problem_solving(
                classification, conversation_state, current_time
            )
        
        # 5. Meeting time management
        if conversation_state.total_duration > 900:  # 15 minutes
            return self._handle_time_management(
                conversation_state, current_time
            )
        
        # No interruption needed
        return InterruptionDecision(
            should_interrupt=False,
            confidence=0.0,
            level=InterruptionLevel.GENTLE_HINT,
            reason="Content is appropriate",
            suggested_message=""
        )
    
    def _handle_off_topic_content(self, classification: TopicClassification,
                                conversation_state: ConversationState,
                                current_time: float) -> InterruptionDecision:
        """Handle off-topic content interruption"""
        
        # Track off-topic duration
        if self.current_off_topic_start is None:
            self.current_off_topic_start = current_time
        
        off_topic_duration = current_time - self.current_off_topic_start
        
        # Determine interruption level based on duration
        if off_topic_duration < 10.0:
            # Give some grace time
            return InterruptionDecision(
                should_interrupt=False,
                confidence=classification.confidence * 0.5,
                level=InterruptionLevel.GENTLE_HINT,
                reason="Off-topic but within grace period",
                suggested_message="",
                wait_time=5.0
            )
        elif off_topic_duration < 20.0:
            level = InterruptionLevel.GENTLE_HINT
            message = "I notice we're getting into some detailed discussion. Should we park this for after the standup?"
        elif off_topic_duration < 30.0:
            level = InterruptionLevel.POLITE_REDIRECT
            message = "Let's try to keep our standup focused. Could we continue this conversation after the meeting?"
        else:
            level = InterruptionLevel.FIRM_REDIRECT
            message = "We need to get back to our standup format. Let's schedule a separate meeting for this discussion."
        
        return InterruptionDecision(
            should_interrupt=True,
            confidence=min(0.9, classification.confidence + off_topic_duration / 30.0),
            level=level,
            reason=f"Off-topic for {off_topic_duration:.1f} seconds",
            suggested_message=message
        )
    
    def _handle_technical_deep_dive(self, classification: TopicClassification,
                                  conversation_state: ConversationState,
                                  current_time: float) -> InterruptionDecision:
        """Handle technical deep dive interruption"""
        
        # Technical content gets less grace time
        if conversation_state.current_speaker_duration < 15.0:
            return InterruptionDecision(
                should_interrupt=False,
                confidence=classification.confidence * 0.6,
                level=InterruptionLevel.GENTLE_HINT,
                reason="Technical content but brief",
                suggested_message=""
            )
        
        # Determine level based on technical indicators
        technical_score = len(classification.technical_indicators) / max(len(classification.keywords), 1)
        
        if technical_score > 0.5:
            level = InterruptionLevel.POLITE_REDIRECT
            message = "This sounds like a technical issue that might need detailed discussion. Could we note this as a blocker and schedule a separate technical session?"
        else:
            level = InterruptionLevel.GENTLE_HINT
            message = "Could you summarize this as a blocker for the standup and we can dive deeper afterwards?"
        
        return InterruptionDecision(
            should_interrupt=True,
            confidence=classification.confidence,
            level=level,
            reason="Technical deep dive detected",
            suggested_message=message
        )
    
    def _handle_excessive_speaking_time(self, conversation_state: ConversationState,
                                      current_time: float) -> InterruptionDecision:
        """Handle excessive speaking time"""
        
        duration = conversation_state.current_speaker_duration
        
        if duration > 180:  # 3 minutes
            level = InterruptionLevel.FIRM_REDIRECT
            message = "You've covered a lot of ground. Could you wrap up your update so we can hear from others?"
        elif duration > 120:  # 2 minutes
            level = InterruptionLevel.POLITE_REDIRECT
            message = "Could you summarize the key points so we can keep the standup moving?"
        else:
            level = InterruptionLevel.GENTLE_HINT
            message = "Just a reminder to keep updates brief so everyone can share."
        
        return InterruptionDecision(
            should_interrupt=True,
            confidence=min(0.9, duration / 120.0),
            level=level,
            reason=f"Speaking for {duration:.1f} seconds",
            suggested_message=message
        )
    
    def _handle_problem_solving(self, classification: TopicClassification,
                              conversation_state: ConversationState,
                              current_time: float) -> InterruptionDecision:
        """Handle problem-solving session interruption"""
        
        if conversation_state.current_speaker_duration < 20.0:
            return InterruptionDecision(
                should_interrupt=False,
                confidence=classification.confidence * 0.7,
                level=InterruptionLevel.GENTLE_HINT,
                reason="Problem-solving but brief",
                suggested_message=""
            )
        
        return InterruptionDecision(
            should_interrupt=True,
            confidence=classification.confidence,
            level=InterruptionLevel.POLITE_REDIRECT,
            reason="Problem-solving session detected",
            suggested_message="This seems like problem-solving that might need more time. Could we add this to our parking lot and address it in a follow-up meeting?"
        )
    
    def _handle_time_management(self, conversation_state: ConversationState,
                              current_time: float) -> InterruptionDecision:
        """Handle overall meeting time management"""
        
        total_minutes = conversation_state.total_duration / 60.0
        
        if total_minutes > 20:  # Meeting too long
            return InterruptionDecision(
                should_interrupt=True,
                confidence=0.8,
                level=InterruptionLevel.POLITE_REDIRECT,
                reason=f"Meeting running long ({total_minutes:.1f} minutes)",
                suggested_message="We're running over time. Let's wrap up the remaining updates quickly."
            )
        
        return InterruptionDecision(
            should_interrupt=False,
            confidence=0.0,
            level=InterruptionLevel.GENTLE_HINT,
            reason="Time management OK",
            suggested_message=""
        )
    
    def _record_interruption(self, decision: InterruptionDecision, timestamp: float):
        """Record an interruption for tracking"""
        
        self.interruption_history.append({
            'timestamp': timestamp,
            'level': decision.level.value,
            'reason': decision.reason,
            'confidence': decision.confidence
        })
        
        self.last_interruption_time = timestamp
        
        # Reset off-topic tracking after interruption
        self.current_off_topic_start = None
        
        logger.info(f"Interruption recorded: {decision.level.value} - {decision.reason}")
    
    def get_interruption_stats(self) -> Dict:
        """Get interruption statistics"""
        
        if not self.interruption_history:
            return {
                'total_interruptions': 0,
                'by_level': {},
                'average_confidence': 0.0,
                'most_common_reason': None
            }
        
        # Count by level
        by_level = {}
        for interruption in self.interruption_history:
            level = interruption['level']
            by_level[level] = by_level.get(level, 0) + 1
        
        # Calculate average confidence
        avg_confidence = sum(i['confidence'] for i in self.interruption_history) / len(self.interruption_history)
        
        # Find most common reason
        reasons = [i['reason'] for i in self.interruption_history]
        most_common_reason = max(set(reasons), key=reasons.count) if reasons else None
        
        return {
            'total_interruptions': len(self.interruption_history),
            'by_level': by_level,
            'average_confidence': avg_confidence,
            'most_common_reason': most_common_reason
        }
    
    def reset_state(self):
        """Reset interruption state for new meeting"""
        self.interruption_history = []
        self.participant_interruptions = {}
        self.current_off_topic_start = None
        self.last_interruption_time = None
        
        logger.info("Interruption Engine state reset")
