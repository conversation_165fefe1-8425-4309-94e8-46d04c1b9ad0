"""
Performance Optimizer

Optimizes the performance of the Meeting Moderator Bot through caching,
batching, and intelligent resource management.
"""

import logging
import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading
from functools import wraps, lru_cache

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    total_requests: int = 0
    total_processing_time: float = 0.0
    average_response_time: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    cache_hit_rate: float = 0.0
    
    # Resource usage
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    
    # Throughput metrics
    requests_per_second: float = 0.0
    peak_requests_per_second: float = 0.0
    
    # Error tracking
    error_count: int = 0
    error_rate: float = 0.0


@dataclass
class CacheConfig:
    """Configuration for caching system"""
    enable_caching: bool = True
    max_cache_size: int = 1000
    cache_ttl_seconds: float = 300.0  # 5 minutes
    enable_classification_cache: bool = True
    enable_suggestion_cache: bool = True
    enable_learning_cache: bool = True


class LRUCache:
    """Thread-safe LRU cache with TTL support"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: float = 300.0):
        """
        Initialize LRU cache
        
        Args:
            max_size: Maximum number of items to cache
            ttl_seconds: Time-to-live for cache entries
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache = {}
        self.access_order = deque()
        self.timestamps = {}
        self.lock = threading.RLock()
        
        self.hits = 0
        self.misses = 0
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self.lock:
            current_time = time.time()
            
            if key in self.cache:
                # Check TTL
                if current_time - self.timestamps[key] > self.ttl_seconds:
                    self._remove_key(key)
                    self.misses += 1
                    return None
                
                # Move to end (most recently used)
                self.access_order.remove(key)
                self.access_order.append(key)
                self.hits += 1
                return self.cache[key]
            
            self.misses += 1
            return None
    
    def put(self, key: str, value: Any) -> None:
        """Put item in cache"""
        with self.lock:
            current_time = time.time()
            
            if key in self.cache:
                # Update existing
                self.cache[key] = value
                self.timestamps[key] = current_time
                self.access_order.remove(key)
                self.access_order.append(key)
            else:
                # Add new
                if len(self.cache) >= self.max_size:
                    # Remove least recently used
                    lru_key = self.access_order.popleft()
                    self._remove_key(lru_key)
                
                self.cache[key] = value
                self.timestamps[key] = current_time
                self.access_order.append(key)
    
    def _remove_key(self, key: str) -> None:
        """Remove key from cache"""
        if key in self.cache:
            del self.cache[key]
            del self.timestamps[key]
            if key in self.access_order:
                self.access_order.remove(key)
    
    def clear(self) -> None:
        """Clear all cache entries"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
            self.access_order.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = self.hits / max(total_requests, 1)
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': hit_rate,
                'ttl_seconds': self.ttl_seconds
            }


class BatchProcessor:
    """Batch processor for grouping similar operations"""
    
    def __init__(self, batch_size: int = 10, flush_interval: float = 1.0):
        """
        Initialize batch processor
        
        Args:
            batch_size: Maximum batch size before auto-flush
            flush_interval: Time interval for auto-flush (seconds)
        """
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        
        self.batches = defaultdict(list)
        self.batch_timestamps = defaultdict(float)
        self.processors = {}
        self.lock = threading.RLock()
        
        # Start background flush task
        self._start_flush_task()
    
    def add_to_batch(self, batch_type: str, item: Any) -> None:
        """Add item to batch"""
        with self.lock:
            current_time = time.time()
            
            if batch_type not in self.batch_timestamps:
                self.batch_timestamps[batch_type] = current_time
            
            self.batches[batch_type].append(item)
            
            # Auto-flush if batch is full
            if len(self.batches[batch_type]) >= self.batch_size:
                self._flush_batch(batch_type)
    
    def register_processor(self, batch_type: str, processor_func: callable) -> None:
        """Register a processor function for a batch type"""
        self.processors[batch_type] = processor_func
    
    def _flush_batch(self, batch_type: str) -> None:
        """Flush a specific batch"""
        if batch_type not in self.batches or not self.batches[batch_type]:
            return
        
        batch_items = self.batches[batch_type].copy()
        self.batches[batch_type].clear()
        self.batch_timestamps[batch_type] = time.time()
        
        # Process batch if processor is registered
        if batch_type in self.processors:
            try:
                self.processors[batch_type](batch_items)
            except Exception as e:
                logger.error(f"Error processing batch {batch_type}: {e}")
    
    def _start_flush_task(self) -> None:
        """Start background task to flush batches periodically"""
        def flush_worker():
            while True:
                try:
                    current_time = time.time()
                    
                    with self.lock:
                        for batch_type, timestamp in list(self.batch_timestamps.items()):
                            if current_time - timestamp >= self.flush_interval:
                                self._flush_batch(batch_type)
                    
                    time.sleep(0.5)  # Check every 500ms
                except Exception as e:
                    logger.error(f"Error in flush worker: {e}")
        
        flush_thread = threading.Thread(target=flush_worker, daemon=True)
        flush_thread.start()
    
    def flush_all(self) -> None:
        """Flush all batches immediately"""
        with self.lock:
            for batch_type in list(self.batches.keys()):
                self._flush_batch(batch_type)


class PerformanceOptimizer:
    """
    Performance optimizer for Meeting Moderator Bot
    
    Provides caching, batching, and performance monitoring capabilities
    to optimize bot performance and resource usage.
    """
    
    def __init__(self, config: CacheConfig):
        """
        Initialize performance optimizer
        
        Args:
            config: Cache configuration
        """
        self.config = config
        
        # Initialize caches
        self.classification_cache = LRUCache(
            max_size=config.max_cache_size,
            ttl_seconds=config.cache_ttl_seconds
        ) if config.enable_classification_cache else None
        
        self.suggestion_cache = LRUCache(
            max_size=config.max_cache_size // 2,
            ttl_seconds=config.cache_ttl_seconds
        ) if config.enable_suggestion_cache else None
        
        self.learning_cache = LRUCache(
            max_size=config.max_cache_size // 4,
            ttl_seconds=config.cache_ttl_seconds * 2  # Longer TTL for learning data
        ) if config.enable_learning_cache else None
        
        # Initialize batch processor
        self.batch_processor = BatchProcessor(batch_size=5, flush_interval=2.0)
        
        # Performance metrics
        self.metrics = PerformanceMetrics()
        self.request_times = deque(maxlen=1000)  # Keep last 1000 request times
        
        # Performance monitoring
        self._start_monitoring()
        
        logger.info("Performance Optimizer initialized")
    
    def cache_classification(self, text: str, meeting_phase: str, context: str = None) -> str:
        """Generate cache key for classification"""
        if not self.config.enable_classification_cache:
            return None
        
        # Create deterministic cache key
        key_parts = [text.strip().lower(), meeting_phase]
        if context:
            key_parts.append(context[:100])  # Limit context length
        
        return f"classification:{':'.join(key_parts)}"
    
    def get_cached_classification(self, cache_key: str) -> Optional[Any]:
        """Get cached classification result"""
        if not self.classification_cache or not cache_key:
            return None
        
        return self.classification_cache.get(cache_key)
    
    def cache_classification_result(self, cache_key: str, result: Any) -> None:
        """Cache classification result"""
        if not self.classification_cache or not cache_key:
            return
        
        self.classification_cache.put(cache_key, result)
    
    def cache_suggestion(self, context_hash: str) -> str:
        """Generate cache key for suggestions"""
        if not self.config.enable_suggestion_cache:
            return None
        
        return f"suggestion:{context_hash}"
    
    def get_cached_suggestions(self, cache_key: str) -> Optional[List[Any]]:
        """Get cached suggestions"""
        if not self.suggestion_cache or not cache_key:
            return None
        
        return self.suggestion_cache.get(cache_key)
    
    def cache_suggestions(self, cache_key: str, suggestions: List[Any]) -> None:
        """Cache suggestions"""
        if not self.suggestion_cache or not cache_key:
            return
        
        self.suggestion_cache.put(cache_key, suggestions)
    
    def get_cached_learning_data(self, team_id: str, data_type: str) -> Optional[Any]:
        """Get cached learning data"""
        if not self.learning_cache:
            return None
        
        cache_key = f"learning:{team_id}:{data_type}"
        return self.learning_cache.get(cache_key)
    
    def cache_learning_data(self, team_id: str, data_type: str, data: Any) -> None:
        """Cache learning data"""
        if not self.learning_cache:
            return
        
        cache_key = f"learning:{team_id}:{data_type}"
        self.learning_cache.put(cache_key, data)
    
    def add_to_batch(self, batch_type: str, item: Any) -> None:
        """Add item to batch for processing"""
        self.batch_processor.add_to_batch(batch_type, item)
    
    def register_batch_processor(self, batch_type: str, processor_func: callable) -> None:
        """Register batch processor function"""
        self.batch_processor.register_processor(batch_type, processor_func)
    
    def track_request(self, processing_time: float, success: bool = True) -> None:
        """Track request performance"""
        self.metrics.total_requests += 1
        self.metrics.total_processing_time += processing_time
        self.metrics.average_response_time = (
            self.metrics.total_processing_time / self.metrics.total_requests
        )
        
        if not success:
            self.metrics.error_count += 1
            self.metrics.error_rate = self.metrics.error_count / self.metrics.total_requests
        
        # Track request times for throughput calculation
        current_time = time.time()
        self.request_times.append(current_time)
        
        # Calculate requests per second (last 60 seconds)
        recent_requests = [t for t in self.request_times if current_time - t <= 60.0]
        self.metrics.requests_per_second = len(recent_requests) / 60.0
        
        if self.metrics.requests_per_second > self.metrics.peak_requests_per_second:
            self.metrics.peak_requests_per_second = self.metrics.requests_per_second
    
    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        # Update cache metrics
        if self.classification_cache:
            cache_stats = self.classification_cache.get_stats()
            self.metrics.cache_hits = cache_stats['hits']
            self.metrics.cache_misses = cache_stats['misses']
            self.metrics.cache_hit_rate = cache_stats['hit_rate']
        
        # Update resource usage
        try:
            import psutil
            process = psutil.Process()
            self.metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
            self.metrics.cpu_usage_percent = process.cpu_percent()
        except ImportError:
            pass  # psutil not available
        
        return self.metrics
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get detailed cache statistics"""
        stats = {}
        
        if self.classification_cache:
            stats['classification'] = self.classification_cache.get_stats()
        
        if self.suggestion_cache:
            stats['suggestion'] = self.suggestion_cache.get_stats()
        
        if self.learning_cache:
            stats['learning'] = self.learning_cache.get_stats()
        
        return stats
    
    def clear_caches(self) -> None:
        """Clear all caches"""
        if self.classification_cache:
            self.classification_cache.clear()
        
        if self.suggestion_cache:
            self.suggestion_cache.clear()
        
        if self.learning_cache:
            self.learning_cache.clear()
        
        logger.info("All caches cleared")
    
    def optimize_for_load(self, expected_load: str) -> None:
        """Optimize settings for expected load"""
        
        if expected_load == "high":
            # High load optimizations
            if self.classification_cache:
                self.classification_cache.max_size = 2000
            if self.suggestion_cache:
                self.suggestion_cache.max_size = 1000
            
            self.batch_processor.batch_size = 20
            self.batch_processor.flush_interval = 0.5
            
        elif expected_load == "low":
            # Low load optimizations
            if self.classification_cache:
                self.classification_cache.max_size = 500
            if self.suggestion_cache:
                self.suggestion_cache.max_size = 250
            
            self.batch_processor.batch_size = 5
            self.batch_processor.flush_interval = 2.0
        
        logger.info(f"Optimized for {expected_load} load")
    
    def _start_monitoring(self) -> None:
        """Start background performance monitoring"""
        def monitor_worker():
            while True:
                try:
                    # Update metrics periodically
                    self.get_performance_metrics()
                    
                    # Log performance warnings
                    if self.metrics.average_response_time > 1.0:
                        logger.warning(f"High average response time: {self.metrics.average_response_time:.2f}s")
                    
                    if self.metrics.cache_hit_rate < 0.5 and self.metrics.total_requests > 100:
                        logger.warning(f"Low cache hit rate: {self.metrics.cache_hit_rate:.2%}")
                    
                    if self.metrics.error_rate > 0.05:
                        logger.warning(f"High error rate: {self.metrics.error_rate:.2%}")
                    
                    time.sleep(30)  # Monitor every 30 seconds
                except Exception as e:
                    logger.error(f"Error in performance monitor: {e}")
        
        monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        monitor_thread.start()


def performance_timer(optimizer: PerformanceOptimizer):
    """Decorator to time function execution and track performance"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                processing_time = time.time() - start_time
                optimizer.track_request(processing_time, success)
        
        return wrapper
    return decorator


def async_performance_timer(optimizer: PerformanceOptimizer):
    """Decorator to time async function execution and track performance"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                processing_time = time.time() - start_time
                optimizer.track_request(processing_time, success)
        
        return wrapper
    return decorator
