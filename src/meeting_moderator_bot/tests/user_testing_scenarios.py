"""
User Testing Scenarios for Meeting Moderator Bot

Comprehensive test scenarios for user acceptance testing and pilot programs.
These scenarios simulate real-world meeting situations to validate bot effectiveness.
"""

import time
import json
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.moderator import MeetingModerator
from ..core.learning_system import FeedbackType, FeedbackRating
from ..utils.config import ModeratorConfig


class ScenarioType(Enum):
    """Types of testing scenarios"""
    BASELINE = "baseline"  # Normal, well-structured standup
    OFF_TOPIC = "off_topic"  # Meeting with off-topic discussions
    TECHNICAL_DEEP_DIVE = "technical_deep_dive"  # Technical discussions
    TIME_MANAGEMENT = "time_management"  # Long-running meeting
    MIXED_ISSUES = "mixed_issues"  # Multiple types of issues
    LEARNING_ADAPTATION = "learning_adaptation"  # Test learning system


@dataclass
class TestMessage:
    """A test message in a scenario"""
    speaker: str
    content: str
    expected_intervention: bool = False
    intervention_type: str = "none"  # interruption, suggestion, phase_change
    delay_seconds: float = 2.0


@dataclass
class TestScenario:
    """A complete test scenario"""
    name: str
    description: str
    scenario_type: ScenarioType
    participants: List[str]
    messages: List[TestMessage]
    expected_outcomes: Dict[str, Any]
    success_criteria: Dict[str, Any]


class UserTestingFramework:
    """
    Framework for conducting user testing scenarios
    
    Provides structured scenarios for testing bot effectiveness
    and collecting user feedback.
    """
    
    def __init__(self, config: ModeratorConfig, api_key: str):
        """
        Initialize testing framework
        
        Args:
            config: Moderator configuration
            api_key: API key for AI services
        """
        self.config = config
        self.api_key = api_key
        self.test_results = []
        
        # Define test scenarios
        self.scenarios = self._create_test_scenarios()
    
    def run_scenario(self, scenario: TestScenario, team_id: str = None) -> Dict[str, Any]:
        """
        Run a specific test scenario
        
        Args:
            scenario: Test scenario to run
            team_id: Optional team identifier for learning
            
        Returns:
            Test results and metrics
        """
        print(f"\n🎭 Running Scenario: {scenario.name}")
        print(f"📝 Description: {scenario.description}")
        print(f"👥 Participants: {', '.join(scenario.participants)}")
        print("-" * 60)
        
        # Initialize moderator
        moderator = MeetingModerator(
            self.config, 
            self.api_key, 
            team_id or f"test_team_{scenario.scenario_type.value}"
        )
        
        # Start meeting
        moderator.start_meeting(scenario.participants)
        
        # Track scenario execution
        interventions = []
        unexpected_interventions = []
        missed_interventions = []
        
        # Process messages
        for i, message in enumerate(scenario.messages):
            print(f"\n{i+1}. {message.speaker}: {message.content}")
            
            # Process speech
            events = moderator.process_speech(message.speaker, message.content)
            
            # Check for interventions
            has_intervention = len(events) > 0
            intervention_types = [event.event_type for event in events]
            
            if has_intervention:
                for event in events:
                    print(f"   🤖 Bot {event.event_type}: {event.message}")
                    interventions.append({
                        'message_index': i,
                        'speaker': message.speaker,
                        'event_type': event.event_type,
                        'confidence': event.confidence,
                        'expected': message.expected_intervention
                    })
            
            # Check expectations
            if message.expected_intervention and not has_intervention:
                missed_interventions.append({
                    'message_index': i,
                    'speaker': message.speaker,
                    'expected_type': message.intervention_type
                })
                print(f"   ❌ Expected {message.intervention_type} but got none")
            elif not message.expected_intervention and has_intervention:
                unexpected_interventions.append({
                    'message_index': i,
                    'speaker': message.speaker,
                    'actual_types': intervention_types
                })
                print(f"   ⚠️ Unexpected intervention: {intervention_types}")
            elif message.expected_intervention and has_intervention:
                print(f"   ✅ Expected intervention received")
            
            # Simulate realistic timing
            time.sleep(0.1)  # Small delay for processing
        
        # End meeting and get summary
        summary = moderator.end_meeting()
        
        # Calculate results
        results = self._calculate_scenario_results(
            scenario, summary, interventions, 
            unexpected_interventions, missed_interventions
        )
        
        # Display results
        self._display_scenario_results(scenario, results)
        
        return results
    
    def run_all_scenarios(self, team_id: str = None) -> Dict[str, Any]:
        """
        Run all test scenarios
        
        Args:
            team_id: Optional team identifier for learning
            
        Returns:
            Aggregated test results
        """
        print("🧪 Running All User Testing Scenarios")
        print("=" * 60)
        
        all_results = {}
        
        for scenario in self.scenarios:
            results = self.run_scenario(scenario, team_id)
            all_results[scenario.name] = results
            self.test_results.append(results)
        
        # Generate overall report
        overall_report = self._generate_overall_report(all_results)
        self._display_overall_report(overall_report)
        
        return overall_report
    
    def collect_user_feedback(self, scenario_name: str, moderator: MeetingModerator, 
                            feedback_data: List[Dict[str, Any]]) -> None:
        """
        Collect user feedback for a scenario
        
        Args:
            scenario_name: Name of the scenario
            moderator: Moderator instance used in scenario
            feedback_data: List of feedback entries
        """
        print(f"\n📝 Collecting User Feedback for: {scenario_name}")
        
        for feedback in feedback_data:
            moderator.collect_feedback(
                feedback_type=feedback.get('type', 'overall'),
                rating=feedback.get('rating', 0),
                participant=feedback.get('participant', 'TestUser'),
                context=feedback.get('context', {}),
                comment=feedback.get('comment', '')
            )
            
            print(f"   📊 {feedback.get('participant', 'TestUser')}: "
                  f"{feedback.get('type', 'overall')} = {feedback.get('rating', 0)}")
    
    def _create_test_scenarios(self) -> List[TestScenario]:
        """Create predefined test scenarios"""
        
        scenarios = []
        
        # Scenario 1: Baseline - Well-structured standup
        scenarios.append(TestScenario(
            name="Baseline Standup",
            description="A well-structured standup meeting with appropriate updates",
            scenario_type=ScenarioType.BASELINE,
            participants=["Alice", "Bob", "Charlie"],
            messages=[
                TestMessage("Alice", "Yesterday I completed the user authentication module. Today I'll work on password reset functionality. No blockers."),
                TestMessage("Bob", "I finished the database migration yesterday. Today I'm starting on API endpoints. I'm blocked on getting staging environment access."),
                TestMessage("Charlie", "Yesterday I worked on the frontend components. Today I'll integrate them with Bob's API. No blockers so far."),
            ],
            expected_outcomes={
                'total_interruptions': 0,
                'off_topic_segments': 0,
                'effectiveness_score': 0.8
            },
            success_criteria={
                'max_interruptions': 1,
                'min_effectiveness': 0.7
            }
        ))
        
        # Scenario 2: Off-topic discussions
        scenarios.append(TestScenario(
            name="Off-Topic Discussion",
            description="Meeting with off-topic discussions that need redirection",
            scenario_type=ScenarioType.OFF_TOPIC,
            participants=["Alice", "Bob", "Charlie"],
            messages=[
                TestMessage("Alice", "Yesterday I worked on the login feature. Today I'll add tests."),
                TestMessage("Bob", "Did you guys see the game last night? It was incredible! The quarterback threw for 400 yards.", 
                          expected_intervention=True, intervention_type="interruption"),
                TestMessage("Charlie", "Oh yeah! And that touchdown in the final minute was amazing! I can't believe they won.", 
                          expected_intervention=True, intervention_type="interruption"),
                TestMessage("Alice", "Anyway, back to work updates. I'm also blocked on the API documentation."),
            ],
            expected_outcomes={
                'total_interruptions': 2,
                'off_topic_segments': 2,
                'effectiveness_score': 0.6
            },
            success_criteria={
                'min_interruptions': 1,
                'max_effectiveness': 0.8
            }
        ))
        
        # Scenario 3: Technical deep-dive
        scenarios.append(TestScenario(
            name="Technical Deep-Dive",
            description="Meeting with technical discussions that should be moved to separate sessions",
            scenario_type=ScenarioType.TECHNICAL_DEEP_DIVE,
            participants=["Alice", "Bob", "David"],
            messages=[
                TestMessage("Alice", "Yesterday I worked on the user service. Today I'll continue with testing."),
                TestMessage("Bob", "I'm having performance issues with the database queries. The N+1 problem is causing timeouts.", 
                          expected_intervention=True, intervention_type="interruption"),
                TestMessage("David", "We should implement eager loading and add proper indexing. Also, we need to consider query optimization and maybe implement caching with Redis.", 
                          expected_intervention=True, intervention_type="interruption"),
                TestMessage("Bob", "Right, and we should also look at the connection pooling configuration and maybe switch to a different ORM.", 
                          expected_intervention=True, intervention_type="interruption"),
            ],
            expected_outcomes={
                'total_interruptions': 2,
                'technical_deep_dives': 1,
                'effectiveness_score': 0.5
            },
            success_criteria={
                'min_interruptions': 1,
                'max_technical_segments': 3
            }
        ))
        
        # Scenario 4: Time management
        scenarios.append(TestScenario(
            name="Long-Running Meeting",
            description="Meeting that runs too long and needs time management",
            scenario_type=ScenarioType.TIME_MANAGEMENT,
            participants=["Alice", "Bob", "Charlie", "David"],
            messages=[
                TestMessage("Alice", "Yesterday I worked on multiple features including authentication, authorization, and user management. Today I'll continue with the profile management system and also work on the settings page."),
                TestMessage("Bob", "I spent yesterday debugging the payment integration which took longer than expected. There were issues with the webhook handling and also some problems with the currency conversion logic."),
                TestMessage("Charlie", "I was working on the frontend redesign and also helping with the mobile app. There are some design inconsistencies we need to address and also performance issues on older devices."),
                TestMessage("David", "I've been working on the infrastructure setup including Docker configuration, Kubernetes deployment, CI/CD pipeline, and also setting up monitoring and logging systems.", 
                          expected_intervention=True, intervention_type="suggestion"),
            ],
            expected_outcomes={
                'total_interruptions': 1,
                'time_management_suggestions': 1,
                'effectiveness_score': 0.4
            },
            success_criteria={
                'min_time_suggestions': 1,
                'max_effectiveness': 0.6
            }
        ))
        
        # Scenario 5: Mixed issues
        scenarios.append(TestScenario(
            name="Mixed Issues Meeting",
            description="Meeting with multiple types of issues requiring different interventions",
            scenario_type=ScenarioType.MIXED_ISSUES,
            participants=["Alice", "Bob", "Charlie"],
            messages=[
                TestMessage("Alice", "Yesterday I worked on the user authentication. Today I'll add tests."),
                TestMessage("Bob", "Did anyone watch the new series on Netflix? It's really good!", 
                          expected_intervention=True, intervention_type="interruption"),
                TestMessage("Charlie", "About the authentication, we need to discuss the JWT implementation details and token refresh strategy.", 
                          expected_intervention=True, intervention_type="interruption"),
                TestMessage("Alice", "I've been working on this for three days now and there are so many edge cases to consider. The password validation alone has fifteen different rules and then there's the two-factor authentication integration.", 
                          expected_intervention=True, intervention_type="suggestion"),
            ],
            expected_outcomes={
                'total_interruptions': 3,
                'mixed_issue_types': True,
                'effectiveness_score': 0.3
            },
            success_criteria={
                'min_interruptions': 2,
                'max_effectiveness': 0.5
            }
        ))
        
        return scenarios
    
    def _calculate_scenario_results(self, scenario: TestScenario, summary: Dict[str, Any],
                                  interventions: List[Dict], unexpected: List[Dict], 
                                  missed: List[Dict]) -> Dict[str, Any]:
        """Calculate results for a scenario"""
        
        # Basic metrics
        total_messages = len(scenario.messages)
        expected_interventions = sum(1 for msg in scenario.messages if msg.expected_intervention)
        actual_interventions = len(interventions)
        
        # Accuracy metrics
        true_positives = len([i for i in interventions if i['expected']])
        false_positives = len(unexpected)
        false_negatives = len(missed)
        
        precision = true_positives / max(actual_interventions, 1)
        recall = true_positives / max(expected_interventions, 1)
        f1_score = 2 * (precision * recall) / max(precision + recall, 0.001)
        
        # Success criteria evaluation
        success_criteria_met = self._evaluate_success_criteria(scenario, summary, interventions)
        
        return {
            'scenario_name': scenario.name,
            'scenario_type': scenario.scenario_type.value,
            'total_messages': total_messages,
            'expected_interventions': expected_interventions,
            'actual_interventions': actual_interventions,
            'true_positives': true_positives,
            'false_positives': false_positives,
            'false_negatives': false_negatives,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'meeting_summary': summary,
            'success_criteria_met': success_criteria_met,
            'overall_success': all(success_criteria_met.values())
        }
    
    def _evaluate_success_criteria(self, scenario: TestScenario, summary: Dict[str, Any], 
                                 interventions: List[Dict]) -> Dict[str, bool]:
        """Evaluate success criteria for a scenario"""
        
        criteria_met = {}
        
        for criterion, value in scenario.success_criteria.items():
            if criterion == 'max_interruptions':
                criteria_met[criterion] = summary['total_interruptions'] <= value
            elif criterion == 'min_interruptions':
                criteria_met[criterion] = summary['total_interruptions'] >= value
            elif criterion == 'min_effectiveness':
                criteria_met[criterion] = summary['effectiveness_score'] >= value
            elif criterion == 'max_effectiveness':
                criteria_met[criterion] = summary['effectiveness_score'] <= value
            elif criterion == 'min_time_suggestions':
                time_suggestions = len([i for i in interventions if 'time' in i.get('event_type', '')])
                criteria_met[criterion] = time_suggestions >= value
            else:
                criteria_met[criterion] = True  # Unknown criterion, assume met
        
        return criteria_met
    
    def _display_scenario_results(self, scenario: TestScenario, results: Dict[str, Any]):
        """Display results for a scenario"""
        
        print(f"\n📊 Results for {scenario.name}:")
        print(f"   Precision: {results['precision']:.2f}")
        print(f"   Recall: {results['recall']:.2f}")
        print(f"   F1 Score: {results['f1_score']:.2f}")
        print(f"   Effectiveness: {results['meeting_summary']['effectiveness_score']:.2f}")
        print(f"   Success: {'✅' if results['overall_success'] else '❌'}")
    
    def _generate_overall_report(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall testing report"""
        
        total_scenarios = len(all_results)
        successful_scenarios = sum(1 for r in all_results.values() if r['overall_success'])
        
        avg_precision = sum(r['precision'] for r in all_results.values()) / total_scenarios
        avg_recall = sum(r['recall'] for r in all_results.values()) / total_scenarios
        avg_f1 = sum(r['f1_score'] for r in all_results.values()) / total_scenarios
        avg_effectiveness = sum(r['meeting_summary']['effectiveness_score'] for r in all_results.values()) / total_scenarios
        
        return {
            'total_scenarios': total_scenarios,
            'successful_scenarios': successful_scenarios,
            'success_rate': successful_scenarios / total_scenarios,
            'average_precision': avg_precision,
            'average_recall': avg_recall,
            'average_f1_score': avg_f1,
            'average_effectiveness': avg_effectiveness,
            'scenario_results': all_results
        }
    
    def _display_overall_report(self, report: Dict[str, Any]):
        """Display overall testing report"""
        
        print(f"\n🎯 Overall Testing Report")
        print("=" * 40)
        print(f"Total Scenarios: {report['total_scenarios']}")
        print(f"Successful Scenarios: {report['successful_scenarios']}")
        print(f"Success Rate: {report['success_rate']:.1%}")
        print(f"Average Precision: {report['average_precision']:.2f}")
        print(f"Average Recall: {report['average_recall']:.2f}")
        print(f"Average F1 Score: {report['average_f1_score']:.2f}")
        print(f"Average Effectiveness: {report['average_effectiveness']:.2f}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if report['average_precision'] < 0.8:
            print("   • Reduce false positive interventions")
        if report['average_recall'] < 0.8:
            print("   • Improve detection of problematic content")
        if report['average_effectiveness'] < 0.7:
            print("   • Enhance overall meeting effectiveness")
        if report['success_rate'] < 0.8:
            print("   • Review and adjust success criteria")


def run_user_testing_demo():
    """Run a demonstration of user testing scenarios"""

    # Setup configuration with Gemini 2.5 Flash
    config = ModeratorConfig()
    config.classification.use_ai_classification = False  # Use rule-based for consistent testing
    config.gemini_model = "gemini-2.5-flash"  # Use latest model
    api_key = "demo_api_key"
    
    # Create testing framework
    testing_framework = UserTestingFramework(config, api_key)
    
    # Run all scenarios
    results = testing_framework.run_all_scenarios("user_testing_team")
    
    return results


if __name__ == "__main__":
    print("🧪 Meeting Moderator Bot - User Testing Scenarios")
    print("=" * 60)
    
    results = run_user_testing_demo()
    
    print(f"\n✅ User testing completed!")
    print(f"Check results for detailed analysis and recommendations.")
