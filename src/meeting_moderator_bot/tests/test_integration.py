"""
Integration tests for Meeting Moderator Bo<PERSON>

Tests the integration between different components and with external platforms.
"""

import unittest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, List, Any

from ..core.moderator import MeetingModerator, MeetingPhase
from ..core.topic_classifier import TopicClassification, IntentAnalysis, SemanticAnalysis
from ..core.suggestion_engine import SuggestionEngine, Suggestion, SuggestionType
from ..core.learning_system import LearningSystem, FeedbackType, FeedbackRating
from ..integrations.base_integration import (
    BaseIntegration, PlatformMessage, PlatformResponse, 
    IntegrationConfig, PlatformType, MessageType
)
from ..integrations.webhook_integration import WebhookIntegration, WebhookConfig
from ..utils.config import ModeratorConfig


class MockIntegration(BaseIntegration):
    """Mock integration for testing"""
    
    def __init__(self, config: IntegrationConfig, moderator_config: ModeratorConfig, gemini_api_key: str):
        super().__init__(config, moderator_config, gemini_api_key)
        self.connected = False
        self.sent_messages = []
        self.joined_meetings = []
    
    async def connect(self) -> bool:
        self.connected = True
        return True
    
    async def disconnect(self) -> None:
        self.connected = False
    
    async def send_message(self, response: PlatformResponse, context: Dict[str, Any]) -> bool:
        self.sent_messages.append((response, context))
        return True
    
    async def join_meeting(self, meeting_id: str) -> bool:
        self.joined_meetings.append(meeting_id)
        return True
    
    async def leave_meeting(self, meeting_id: str) -> None:
        if meeting_id in self.joined_meetings:
            self.joined_meetings.remove(meeting_id)


class TestBaseIntegration(unittest.TestCase):
    """Test cases for base integration functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = IntegrationConfig(
            platform_type=PlatformType.CUSTOM,
            api_credentials={},
            rate_limits={'default': 10}
        )
        self.moderator_config = ModeratorConfig()
        self.api_key = "test_api_key"
        
        self.integration = MockIntegration(self.config, self.moderator_config, self.api_key)
    
    def test_initialization(self):
        """Test integration initialization"""
        self.assertIsNotNone(self.integration)
        self.assertEqual(self.integration.config.platform_type, PlatformType.CUSTOM)
        self.assertFalse(self.integration.connected)
        self.assertEqual(len(self.integration.active_moderators), 0)
    
    async def test_connection_lifecycle(self):
        """Test connection and disconnection"""
        # Test connection
        result = await self.integration.connect()
        self.assertTrue(result)
        self.assertTrue(self.integration.connected)
        
        # Test disconnection
        await self.integration.disconnect()
        self.assertFalse(self.integration.connected)
    
    async def test_message_processing(self):
        """Test message processing workflow"""
        # Create test message
        message = PlatformMessage(
            message_id="test_001",
            sender="Alice",
            content="Yesterday I worked on the login feature. Today I'll add tests.",
            timestamp=time.time(),
            message_type=MessageType.TEXT,
            channel_id="test_channel"
        )
        
        # Mock the moderator components to avoid API calls
        with patch.object(self.integration, '_get_or_create_moderator') as mock_moderator:
            mock_mod = Mock()
            mock_mod.process_speech.return_value = []
            mock_moderator.return_value = mock_mod
            
            # Process message
            responses = await self.integration.process_message(message)
            
            # Verify processing
            mock_mod.process_speech.assert_called_once_with("Alice", message.content)
            self.assertIsInstance(responses, list)
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        rate_limiter = self.integration.rate_limiter
        
        # Test normal requests
        for i in range(5):
            self.assertTrue(rate_limiter.allow_request("user1"))
        
        # Test rate limit exceeded (default is 10 per minute)
        for i in range(10):
            rate_limiter.allow_request("user2")
        
        # Should be rate limited now
        self.assertFalse(rate_limiter.allow_request("user2"))
        
        # Different user should still work
        self.assertTrue(rate_limiter.allow_request("user3"))


class TestWebhookIntegration(unittest.TestCase):
    """Test cases for webhook integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = WebhookConfig(
            api_credentials={},
            host="localhost",
            port=8081,
            webhook_secret="test_secret"
        )
        self.moderator_config = ModeratorConfig()
        self.api_key = "test_api_key"
        
        # Mock FastAPI to avoid actual server startup
        with patch('meeting_moderator_bot.integrations.webhook_integration.FASTAPI_AVAILABLE', True):
            with patch('meeting_moderator_bot.integrations.webhook_integration.FastAPI'):
                self.webhook = WebhookIntegration(self.config, self.moderator_config, self.api_key)
    
    def test_webhook_initialization(self):
        """Test webhook integration initialization"""
        self.assertIsNotNone(self.webhook)
        self.assertEqual(self.webhook.config.platform_type, PlatformType.WEBHOOK)
        self.assertEqual(self.webhook.webhook_config.port, 8081)
    
    def test_webhook_message_conversion(self):
        """Test conversion of webhook data to PlatformMessage"""
        # Mock request object
        mock_request = Mock()
        mock_request.client.host = "127.0.0.1"
        mock_request.headers.get.return_value = "test-agent"
        
        webhook_data = {
            'message_id': 'webhook_001',
            'sender': 'Bob',
            'content': 'This is a test message',
            'timestamp': time.time(),
            'meeting_id': 'meeting_123'
        }
        
        message = self.webhook._convert_webhook_to_message(webhook_data, mock_request)
        
        self.assertEqual(message.message_id, 'webhook_001')
        self.assertEqual(message.sender, 'Bob')
        self.assertEqual(message.content, 'This is a test message')
        self.assertEqual(message.meeting_id, 'meeting_123')
        self.assertEqual(message.message_type, MessageType.TEXT)
    
    def test_webhook_response_formatting(self):
        """Test formatting of responses for webhook"""
        responses = [
            PlatformResponse(
                message="Please keep the discussion focused on standup updates.",
                response_type='text',
                is_urgent=True,
                metadata={'event_type': 'interruption', 'confidence': 0.8}
            )
        ]
        
        original_data = {'message_id': 'test_001'}
        
        formatted = self.webhook._format_webhook_response(responses, original_data)
        
        self.assertEqual(formatted['status'], 'success')
        self.assertEqual(len(formatted['responses']), 1)
        self.assertEqual(formatted['responses'][0]['message'], responses[0].message)
        self.assertTrue(formatted['responses'][0]['urgent'])


class TestEndToEndIntegration(unittest.TestCase):
    """End-to-end integration tests"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = IntegrationConfig(
            platform_type=PlatformType.CUSTOM,
            api_credentials={},
            rate_limits={'default': 100}
        )
        self.moderator_config = ModeratorConfig()
        self.moderator_config.classification.use_ai_classification = False  # Use rule-based for tests
        self.api_key = "test_api_key"
        
        self.integration = MockIntegration(self.config, self.moderator_config, self.api_key)
    
    async def test_complete_meeting_workflow(self):
        """Test complete meeting workflow from start to finish"""
        await self.integration.connect()
        
        # Simulate meeting start
        await self.integration.handle_meeting_event('meeting_started', {
            'meeting_id': 'test_meeting_001',
            'participants': ['Alice', 'Bob', 'Charlie']
        })
        
        # Simulate conversation messages
        messages = [
            ("Alice", "Yesterday I worked on the user authentication. Today I'll continue with testing."),
            ("Bob", "I finished the database migration. Today I'm working on API endpoints. I'm blocked on staging access."),
            ("Charlie", "Did you guys see the game last night? It was amazing!"),  # Off-topic
            ("Alice", "About the database migration, I think we should discuss the indexing strategy..."),  # Technical deep-dive
        ]
        
        responses_received = []
        
        for sender, content in messages:
            message = PlatformMessage(
                message_id=f"msg_{int(time.time())}",
                sender=sender,
                content=content,
                timestamp=time.time(),
                message_type=MessageType.TEXT,
                meeting_id='test_meeting_001'
            )
            
            responses = await self.integration.process_message(message)
            responses_received.extend(responses)
        
        # Verify that off-topic and technical messages generated responses
        self.assertGreater(len(responses_received), 0)
        
        # Check that some responses are interruptions
        interruption_responses = [r for r in responses_received if r.metadata.get('event_type') == 'interruption']
        self.assertGreater(len(interruption_responses), 0)
        
        # Simulate meeting end
        await self.integration.handle_meeting_event('meeting_ended', {
            'meeting_id': 'test_meeting_001'
        })
        
        # Verify meeting was cleaned up
        self.assertNotIn('test_meeting_001', self.integration.active_moderators)
    
    async def test_feedback_collection_and_learning(self):
        """Test feedback collection and learning system"""
        await self.integration.connect()
        
        # Start a meeting
        meeting_id = 'learning_test_meeting'
        await self.integration.handle_meeting_event('meeting_started', {
            'meeting_id': meeting_id,
            'participants': ['Alice', 'Bob']
        })
        
        # Get the moderator
        moderator = self.integration.active_moderators[meeting_id]
        
        # Collect some feedback
        moderator.collect_feedback(
            feedback_type="interruption",
            rating=1,  # Positive
            participant="Alice",
            context={"interruption_reason": "off_topic"},
            comment="Good interruption, kept us focused"
        )
        
        moderator.collect_feedback(
            feedback_type="suggestion",
            rating=-1,  # Negative
            participant="Bob",
            context={"suggestion_type": "time_management"},
            comment="Too many suggestions"
        )
        
        # Check that feedback was recorded
        team_analysis = moderator.get_team_analysis()
        self.assertGreater(team_analysis['feedback_count'], 0)
        
        # Check adaptations
        stats = moderator.get_statistics()
        self.assertIn('team_adaptations', stats)
        self.assertIn('learning_analytics', stats)
    
    async def test_suggestion_system_integration(self):
        """Test suggestion system integration"""
        await self.integration.connect()
        
        # Create a scenario that should trigger suggestions
        message = PlatformMessage(
            message_id="suggestion_test",
            sender="Alice",
            content="We've been talking for 20 minutes now about various technical details...",
            timestamp=time.time(),
            message_type=MessageType.TEXT,
            meeting_id='suggestion_test_meeting'
        )
        
        # Mock conversation state to trigger time management suggestions
        with patch.object(self.integration, '_get_or_create_moderator') as mock_get_moderator:
            mock_moderator = Mock()
            
            # Mock a long-running conversation
            mock_conversation_state = Mock()
            mock_conversation_state.total_duration = 1200.0  # 20 minutes
            mock_conversation_state.current_speaker_duration = 180.0  # 3 minutes
            
            # Mock moderation events including suggestions
            mock_events = [
                Mock(
                    event_type='suggestion',
                    message='We\'re running over time. Let\'s wrap up the remaining updates.',
                    confidence=0.8,
                    context={'suggestion_type': 'time_management'}
                )
            ]
            mock_moderator.process_speech.return_value = mock_events
            mock_get_moderator.return_value = mock_moderator
            
            # Process message
            responses = await self.integration.process_message(message)
            
            # Verify suggestion was generated
            suggestion_responses = [r for r in responses if r.metadata.get('event_type') == 'suggestion']
            self.assertGreater(len(suggestion_responses), 0)


class TestPerformanceAndScalability(unittest.TestCase):
    """Test performance and scalability aspects"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = IntegrationConfig(
            platform_type=PlatformType.CUSTOM,
            api_credentials={},
            rate_limits={'default': 1000}
        )
        self.moderator_config = ModeratorConfig()
        self.moderator_config.classification.use_ai_classification = False
        self.api_key = "test_api_key"
        
        self.integration = MockIntegration(self.config, self.moderator_config, self.api_key)
    
    async def test_concurrent_message_processing(self):
        """Test processing multiple messages concurrently"""
        await self.integration.connect()
        
        # Create multiple messages
        messages = []
        for i in range(10):
            message = PlatformMessage(
                message_id=f"concurrent_msg_{i}",
                sender=f"User{i % 3}",  # 3 different users
                content=f"This is test message number {i}",
                timestamp=time.time(),
                message_type=MessageType.TEXT,
                meeting_id='concurrent_test_meeting'
            )
            messages.append(message)
        
        # Process messages concurrently
        start_time = time.time()
        tasks = [self.integration.process_message(msg) for msg in messages]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Verify all messages were processed
        self.assertEqual(len(results), 10)
        
        # Verify processing was reasonably fast (should be under 1 second for rule-based)
        processing_time = end_time - start_time
        self.assertLess(processing_time, 5.0)  # Allow 5 seconds for safety
        
        logger.info(f"Processed {len(messages)} messages in {processing_time:.2f} seconds")
    
    async def test_multiple_meetings_handling(self):
        """Test handling multiple concurrent meetings"""
        await self.integration.connect()
        
        # Start multiple meetings
        meeting_ids = [f"meeting_{i}" for i in range(5)]
        
        for meeting_id in meeting_ids:
            await self.integration.handle_meeting_event('meeting_started', {
                'meeting_id': meeting_id,
                'participants': ['Alice', 'Bob', 'Charlie']
            })
        
        # Verify all meetings have moderators
        self.assertEqual(len(self.integration.active_moderators), 5)
        
        # Send messages to different meetings
        for i, meeting_id in enumerate(meeting_ids):
            message = PlatformMessage(
                message_id=f"multi_meeting_msg_{i}",
                sender="Alice",
                content=f"Update for meeting {i}",
                timestamp=time.time(),
                message_type=MessageType.TEXT,
                meeting_id=meeting_id
            )
            
            responses = await self.integration.process_message(message)
            # Each message should be processed independently
            self.assertIsInstance(responses, list)
        
        # End all meetings
        for meeting_id in meeting_ids:
            await self.integration.handle_meeting_event('meeting_ended', {
                'meeting_id': meeting_id
            })
        
        # Verify cleanup
        self.assertEqual(len(self.integration.active_moderators), 0)


# Async test runner helper
def run_async_test(test_func):
    """Helper to run async test functions"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(test_func())
    finally:
        loop.close()


if __name__ == '__main__':
    # Add async test methods to test classes
    for test_class in [TestBaseIntegration, TestEndToEndIntegration, TestPerformanceAndScalability]:
        for method_name in dir(test_class):
            if method_name.startswith('test_') and asyncio.iscoroutinefunction(getattr(test_class, method_name)):
                # Wrap async test methods
                original_method = getattr(test_class, method_name)
                setattr(test_class, method_name, lambda self, method=original_method: run_async_test(lambda: method(self)))
    
    unittest.main()
