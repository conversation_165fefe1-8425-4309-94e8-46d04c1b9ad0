"""
Tests for Learning and Adaptation System

Tests the feedback collection, team profiling, and adaptation capabilities
of the Meeting Moderator Bot.
"""

import unittest
import tempfile
import shutil
import time
import json
from pathlib import Path
from unittest.mock import Mock, patch

from ..core.learning_system import (
    LearningSystem, LearningConfig, FeedbackEntry, TeamProfile,
    FeedbackType, FeedbackRating
)


class TestLearningSystem(unittest.TestCase):
    """Test cases for the learning system"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create temporary directory for test data
        self.temp_dir = tempfile.mkdtemp()
        
        self.config = LearningConfig(
            enable_learning=True,
            feedback_weight=0.3,
            adaptation_threshold=3,  # Lower threshold for testing
            max_feedback_age=86400.0  # 1 day
        )
        
        self.learning_system = LearningSystem(self.config, self.temp_dir)
    
    def tearDown(self):
        """Clean up test fixtures"""
        # Remove temporary directory
        shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """Test learning system initialization"""
        self.assertIsNotNone(self.learning_system)
        self.assertEqual(self.learning_system.config.adaptation_threshold, 3)
        self.assertEqual(len(self.learning_system.feedback_entries), 0)
        self.assertEqual(len(self.learning_system.team_profiles), 0)
    
    def test_feedback_collection(self):
        """Test feedback collection functionality"""
        # Collect feedback
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.INTERRUPTION_FEEDBACK,
            rating=FeedbackRating.POSITIVE,
            participant="Alice",
            team_id="team_001",
            context={"interruption_reason": "off_topic"},
            comment="Good interruption, kept us focused"
        )
        
        # Verify feedback was stored
        self.assertEqual(len(self.learning_system.feedback_entries), 1)
        feedback = self.learning_system.feedback_entries[0]
        
        self.assertEqual(feedback.feedback_type, FeedbackType.INTERRUPTION_FEEDBACK)
        self.assertEqual(feedback.rating, FeedbackRating.POSITIVE)
        self.assertEqual(feedback.participant, "Alice")
        self.assertEqual(feedback.team_id, "team_001")
        self.assertEqual(feedback.comment, "Good interruption, kept us focused")
    
    def test_team_profile_creation(self):
        """Test team profile creation and updates"""
        team_id = "test_team_001"
        
        # Collect feedback to trigger profile creation
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.OVERALL_MEETING,
            rating=FeedbackRating.POSITIVE,
            participant="Bob",
            team_id=team_id,
            context={"meeting_duration": 900}
        )
        
        # Verify team profile was created
        self.assertIn(team_id, self.learning_system.team_profiles)
        profile = self.learning_system.team_profiles[team_id]
        
        self.assertEqual(profile.team_id, team_id)
        self.assertEqual(profile.feedback_count, 1)
        self.assertGreater(profile.satisfaction_score, 0.5)  # Positive feedback should increase score
        self.assertIn("Bob", profile.typical_participants)
    
    def test_personality_learning(self):
        """Test learning personality preferences"""
        team_id = "personality_test_team"
        
        # Give positive feedback for friendly personality
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.PERSONALITY_FEEDBACK,
            rating=FeedbackRating.VERY_POSITIVE,
            participant="Charlie",
            team_id=team_id,
            context={"personality": "friendly"}
        )
        
        profile = self.learning_system.team_profiles[team_id]
        self.assertEqual(profile.preferred_personality, "friendly")
        
        # Give negative feedback for assertive personality
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.PERSONALITY_FEEDBACK,
            rating=FeedbackRating.NEGATIVE,
            participant="Charlie",
            team_id=team_id,
            context={"personality": "assertive"}
        )
        
        # Should still prefer friendly (positive feedback was stronger)
        self.assertEqual(profile.preferred_personality, "friendly")
    
    def test_interruption_sensitivity_learning(self):
        """Test learning interruption sensitivity"""
        team_id = "interruption_test_team"
        
        # Initial sensitivity should be default (0.7)
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.INTERRUPTION_FEEDBACK,
            rating=FeedbackRating.NEUTRAL,
            participant="David",
            team_id=team_id,
            context={}
        )
        
        profile = self.learning_system.team_profiles[team_id]
        initial_sensitivity = profile.interruption_sensitivity
        
        # Give positive feedback on interruptions
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.INTERRUPTION_FEEDBACK,
            rating=FeedbackRating.POSITIVE,
            participant="David",
            team_id=team_id,
            context={}
        )
        
        # Sensitivity should increase
        self.assertGreater(profile.interruption_sensitivity, initial_sensitivity)
        
        # Give negative feedback on interruptions
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.INTERRUPTION_FEEDBACK,
            rating=FeedbackRating.NEGATIVE,
            participant="David",
            team_id=team_id,
            context={}
        )
        
        # Sensitivity should decrease
        self.assertLess(profile.interruption_sensitivity, initial_sensitivity + 0.1)
    
    def test_adaptation_triggering(self):
        """Test when adaptations are triggered"""
        team_id = "adaptation_test_team"
        
        # Collect feedback below threshold
        for i in range(2):  # Below threshold of 3
            self.learning_system.collect_feedback(
                feedback_type=FeedbackType.OVERALL_MEETING,
                rating=FeedbackRating.NEGATIVE,
                participant=f"User{i}",
                team_id=team_id,
                context={}
            )
        
        profile = self.learning_system.team_profiles[team_id]
        initial_sensitivity = profile.interruption_sensitivity
        
        # Add one more feedback to reach threshold
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.OVERALL_MEETING,
            rating=FeedbackRating.NEGATIVE,
            participant="User3",
            team_id=team_id,
            context={}
        )
        
        # Adaptations should have been triggered
        # With negative feedback, interruption sensitivity should decrease
        self.assertLess(profile.interruption_sensitivity, initial_sensitivity)
    
    def test_team_adaptations_retrieval(self):
        """Test retrieving team-specific adaptations"""
        team_id = "adaptations_test_team"
        
        # Create a team profile with specific preferences
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.PERSONALITY_FEEDBACK,
            rating=FeedbackRating.POSITIVE,
            participant="Eve",
            team_id=team_id,
            context={"personality": "assertive"}
        )
        
        # Get adaptations
        adaptations = self.learning_system.get_team_adaptations(team_id)
        
        # Verify adaptations structure
        self.assertIn('personality', adaptations)
        self.assertIn('interruption_threshold', adaptations)
        self.assertIn('suggestion_frequency_modifier', adaptations)
        self.assertIn('technical_tolerance', adaptations)
        self.assertIn('max_meeting_duration', adaptations)
        self.assertIn('politeness_factor', adaptations)
        
        # Verify personality was learned
        self.assertEqual(adaptations['personality'], 'assertive')
    
    def test_team_pattern_analysis(self):
        """Test team pattern analysis"""
        team_id = "pattern_analysis_team"
        
        # Collect various types of feedback
        feedback_data = [
            (FeedbackType.INTERRUPTION_FEEDBACK, FeedbackRating.POSITIVE, "Good timing"),
            (FeedbackType.SUGGESTION_FEEDBACK, FeedbackRating.NEGATIVE, "Too many suggestions"),
            (FeedbackType.OVERALL_MEETING, FeedbackRating.POSITIVE, "Great meeting"),
            (FeedbackType.TIMING_FEEDBACK, FeedbackRating.NEUTRAL, "Timing was okay")
        ]
        
        for feedback_type, rating, comment in feedback_data:
            self.learning_system.collect_feedback(
                feedback_type=feedback_type,
                rating=rating,
                participant="Analyst",
                team_id=team_id,
                context={},
                comment=comment
            )
        
        # Analyze patterns
        analysis = self.learning_system.analyze_team_patterns(team_id)
        
        # Verify analysis structure
        self.assertIn('team_id', analysis)
        self.assertIn('feedback_count', analysis)
        self.assertIn('satisfaction_score', analysis)
        self.assertIn('common_issues', analysis)
        self.assertIn('improvement_areas', analysis)
        self.assertIn('recommendations', analysis)
        
        # Verify data
        self.assertEqual(analysis['team_id'], team_id)
        self.assertEqual(analysis['feedback_count'], 4)
    
    def test_learning_analytics(self):
        """Test learning analytics generation"""
        # Create multiple teams with feedback
        teams = ["analytics_team_1", "analytics_team_2", "analytics_team_3"]
        
        for i, team_id in enumerate(teams):
            # Give different satisfaction levels
            rating = [FeedbackRating.NEGATIVE, FeedbackRating.NEUTRAL, FeedbackRating.POSITIVE][i]
            
            self.learning_system.collect_feedback(
                feedback_type=FeedbackType.OVERALL_MEETING,
                rating=rating,
                participant=f"User{i}",
                team_id=team_id,
                context={}
            )
        
        # Get analytics
        analytics = self.learning_system.get_learning_analytics()
        
        # Verify analytics structure
        self.assertIn('learning_stats', analytics)
        self.assertIn('team_count', analytics)
        self.assertIn('average_satisfaction', analytics)
        self.assertIn('team_satisfaction', analytics)
        self.assertIn('feedback_distribution', analytics)
        
        # Verify data
        self.assertEqual(analytics['team_count'], 3)
        self.assertEqual(analytics['learning_stats']['total_feedback'], 3)
        self.assertIn('overall_meeting_feedback', analytics['feedback_distribution'])
    
    def test_data_persistence(self):
        """Test saving and loading learning data"""
        team_id = "persistence_test_team"
        
        # Add some data
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.INTERRUPTION_FEEDBACK,
            rating=FeedbackRating.POSITIVE,
            participant="Persistent",
            team_id=team_id,
            context={"test": "data"}
        )
        
        # Save data
        self.learning_system.save_data()
        
        # Verify files were created
        profiles_file = Path(self.temp_dir) / "team_profiles.json"
        feedback_file = Path(self.temp_dir) / "feedback_entries.json"
        
        self.assertTrue(profiles_file.exists())
        self.assertTrue(feedback_file.exists())
        
        # Create new learning system and load data
        new_learning_system = LearningSystem(self.config, self.temp_dir)
        
        # Verify data was loaded
        self.assertEqual(len(new_learning_system.team_profiles), 1)
        self.assertEqual(len(new_learning_system.feedback_entries), 1)
        self.assertIn(team_id, new_learning_system.team_profiles)
    
    def test_old_data_cleanup(self):
        """Test cleanup of old feedback data"""
        team_id = "cleanup_test_team"
        
        # Add old feedback (simulate by setting old timestamp)
        old_feedback = FeedbackEntry(
            feedback_type=FeedbackType.OVERALL_MEETING,
            rating=FeedbackRating.NEUTRAL,
            timestamp=time.time() - 2592000,  # 30 days ago
            participant="OldUser",
            team_id=team_id,
            context={}
        )
        
        # Add recent feedback
        self.learning_system.collect_feedback(
            feedback_type=FeedbackType.OVERALL_MEETING,
            rating=FeedbackRating.POSITIVE,
            participant="NewUser",
            team_id=team_id,
            context={}
        )
        
        # Manually add old feedback to test cleanup
        self.learning_system.feedback_entries.append(old_feedback)
        
        # Verify we have 2 feedback entries
        self.assertEqual(len(self.learning_system.feedback_entries), 2)
        
        # Run cleanup
        self.learning_system.cleanup_old_data()
        
        # Verify old feedback was removed
        self.assertEqual(len(self.learning_system.feedback_entries), 1)
        self.assertEqual(self.learning_system.feedback_entries[0].participant, "NewUser")
    
    def test_default_adaptations(self):
        """Test default adaptations for new teams"""
        # Get adaptations for non-existent team
        adaptations = self.learning_system.get_team_adaptations("non_existent_team")
        
        # Should return default values
        self.assertEqual(adaptations['personality'], 'professional')
        self.assertEqual(adaptations['interruption_threshold'], 0.7)
        self.assertEqual(adaptations['suggestion_frequency_modifier'], 0.5)
        self.assertEqual(adaptations['technical_tolerance'], 0.5)
        self.assertEqual(adaptations['max_meeting_duration'], 900.0)
        self.assertEqual(adaptations['politeness_factor'], 0.8)


class TestFeedbackEntry(unittest.TestCase):
    """Test cases for FeedbackEntry data class"""
    
    def test_feedback_entry_creation(self):
        """Test creating feedback entries"""
        feedback = FeedbackEntry(
            feedback_type=FeedbackType.SUGGESTION_FEEDBACK,
            rating=FeedbackRating.VERY_POSITIVE,
            timestamp=time.time(),
            participant="TestUser",
            team_id="test_team",
            context={"suggestion_type": "time_management"},
            comment="Very helpful suggestion"
        )
        
        self.assertEqual(feedback.feedback_type, FeedbackType.SUGGESTION_FEEDBACK)
        self.assertEqual(feedback.rating, FeedbackRating.VERY_POSITIVE)
        self.assertEqual(feedback.participant, "TestUser")
        self.assertEqual(feedback.team_id, "test_team")
        self.assertEqual(feedback.comment, "Very helpful suggestion")


class TestTeamProfile(unittest.TestCase):
    """Test cases for TeamProfile data class"""
    
    def test_team_profile_creation(self):
        """Test creating team profiles"""
        profile = TeamProfile(
            team_id="profile_test_team",
            team_name="Test Team",
            preferred_personality="friendly",
            interruption_sensitivity=0.8,
            suggestion_frequency=0.6
        )
        
        self.assertEqual(profile.team_id, "profile_test_team")
        self.assertEqual(profile.team_name, "Test Team")
        self.assertEqual(profile.preferred_personality, "friendly")
        self.assertEqual(profile.interruption_sensitivity, 0.8)
        self.assertEqual(profile.suggestion_frequency, 0.6)
        
        # Test default values
        self.assertEqual(profile.average_meeting_duration, 900.0)
        self.assertEqual(len(profile.typical_participants), 0)
        self.assertEqual(len(profile.common_topics), 0)


if __name__ == '__main__':
    unittest.main()
