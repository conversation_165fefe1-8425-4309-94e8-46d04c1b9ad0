"""
Unit tests for Meeting Moderator core functionality

Tests the main MeetingModerator class and its integration with other components.
"""

import unittest
import time
from unittest.mock import Mock, patch

from ..core.moderator import MeetingModerator, MeetingPhase, ModerationEvent
from ..utils.config import ModeratorConfig


class TestMeetingModerator(unittest.TestCase):
    """Test cases for MeetingModerator class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = ModeratorConfig()
        self.api_key = "test_api_key"
        
        # Mock the Gemini API to avoid actual API calls in tests
        with patch('google.generativeai.configure'):
            with patch('google.generativeai.GenerativeModel'):
                self.moderator = MeetingModerator(self.config, self.api_key)
    
    def test_initialization(self):
        """Test moderator initialization"""
        self.assertIsNotNone(self.moderator)
        self.assertEqual(self.moderator.current_phase, MeetingPhase.OPENING)
        self.assertIsNone(self.moderator.meeting_start_time)
        self.assertEqual(len(self.moderator.participants), 0)
    
    def test_start_meeting(self):
        """Test starting a meeting"""
        participants = ["<PERSON>", "<PERSON>", "<PERSON>"]
        
        self.moderator.start_meeting(participants)
        
        self.assertIsNotNone(self.moderator.meeting_start_time)
        self.assertEqual(len(self.moderator.participants), 3)
        self.assertEqual(self.moderator.current_phase, MeetingPhase.OPENING)
        
        # Check that welcome event was generated
        self.assertEqual(len(self.moderator.moderation_events), 1)
        welcome_event = self.moderator.moderation_events[0]
        self.assertEqual(welcome_event.event_type, 'suggestion')
    
    def test_process_speech_standup_content(self):
        """Test processing standup-relevant speech"""
        participants = ["Alice", "Bob"]
        self.moderator.start_meeting(participants)
        
        # Mock the topic classifier to return standup-relevant content
        with patch.object(self.moderator.topic_classifier, 'classify_text') as mock_classify:
            mock_classify.return_value = Mock(
                is_standup_relevant=True,
                topic_type='standup_update',
                confidence=0.9
            )
            
            # Mock the interruption engine to not interrupt
            with patch.object(self.moderator.interruption_engine, 'should_interrupt') as mock_interrupt:
                mock_interrupt.return_value = Mock(should_interrupt=False)
                
                events = self.moderator.process_speech(
                    "Alice", 
                    "Yesterday I worked on the user authentication feature. Today I'll continue with testing."
                )
                
                # Should not generate interruption events for good standup content
                interruption_events = [e for e in events if e.event_type == 'interruption']
                self.assertEqual(len(interruption_events), 0)
    
    def test_process_speech_off_topic_content(self):
        """Test processing off-topic speech"""
        participants = ["Alice", "Bob"]
        self.moderator.start_meeting(participants)
        
        # Mock the topic classifier to return off-topic content
        with patch.object(self.moderator.topic_classifier, 'classify_text') as mock_classify:
            mock_classify.return_value = Mock(
                is_standup_relevant=False,
                topic_type='off_topic_discussion',
                confidence=0.8
            )
            
            # Mock the interruption engine to interrupt
            with patch.object(self.moderator.interruption_engine, 'should_interrupt') as mock_interrupt:
                mock_interrupt.return_value = Mock(
                    should_interrupt=True,
                    confidence=0.8,
                    reason="Off-topic discussion"
                )
                
                events = self.moderator.process_speech(
                    "Alice", 
                    "Did you see the game last night? It was amazing!"
                )
                
                # Should generate interruption event for off-topic content
                interruption_events = [e for e in events if e.event_type == 'interruption']
                self.assertEqual(len(interruption_events), 1)
                self.assertEqual(interruption_events[0].participant, "Alice")
    
    def test_end_meeting(self):
        """Test ending a meeting"""
        participants = ["Alice", "Bob"]
        self.moderator.start_meeting(participants)
        
        # Simulate some meeting activity
        time.sleep(0.1)  # Small delay to ensure duration > 0
        
        summary = self.moderator.end_meeting()
        
        self.assertIsInstance(summary, dict)
        self.assertIn('duration_minutes', summary)
        self.assertIn('total_interruptions', summary)
        self.assertIn('effectiveness_score', summary)
        self.assertGreater(summary['duration_minutes'], 0)
    
    def test_statistics_tracking(self):
        """Test statistics tracking"""
        participants = ["Alice", "Bob"]
        self.moderator.start_meeting(participants)
        
        stats = self.moderator.get_statistics()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('current_phase', stats)
        self.assertIn('participants', stats)
        self.assertIn('effectiveness_score', stats)
        self.assertEqual(stats['current_phase'], MeetingPhase.OPENING.value)
    
    def test_phase_transitions(self):
        """Test meeting phase transitions"""
        participants = ["Alice", "Bob"]
        self.moderator.start_meeting(participants)
        
        # Mock conversation state to trigger phase transition
        mock_conversation_state = Mock()
        mock_conversation_state.total_segments = 5  # Enough to trigger transition
        
        phase_event = self.moderator._check_phase_transition(
            mock_conversation_state, time.time()
        )
        
        self.assertIsNotNone(phase_event)
        self.assertEqual(phase_event.event_type, 'phase_change')
        self.assertEqual(self.moderator.current_phase, MeetingPhase.INDIVIDUAL_UPDATES)
    
    def test_time_management(self):
        """Test time management suggestions"""
        participants = ["Alice"]
        self.moderator.start_meeting(participants)
        
        # Simulate excessive speaking time
        self.moderator.participants["Alice"]['speaking_time'] = 150.0  # 2.5 minutes
        
        time_event = self.moderator._check_time_management("Alice", time.time())
        
        self.assertIsNotNone(time_event)
        self.assertEqual(time_event.event_type, 'suggestion')
        self.assertEqual(time_event.participant, "Alice")
    
    def test_effectiveness_score_calculation(self):
        """Test effectiveness score calculation"""
        participants = ["Alice", "Bob"]
        self.moderator.start_meeting(participants)
        
        # Set some statistics
        self.moderator.stats['total_interruptions'] = 2
        self.moderator.stats['off_topic_segments'] = 1
        self.moderator.stats['meeting_duration'] = 900  # 15 minutes
        
        score = self.moderator._calculate_effectiveness_score()
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 1.0)


class TestModerationEvent(unittest.TestCase):
    """Test cases for ModerationEvent data class"""
    
    def test_moderation_event_creation(self):
        """Test creating a moderation event"""
        event = ModerationEvent(
            timestamp=time.time(),
            event_type='interruption',
            participant='Alice',
            message='Please focus on standup updates',
            confidence=0.8,
            context={'reason': 'off_topic'}
        )
        
        self.assertEqual(event.event_type, 'interruption')
        self.assertEqual(event.participant, 'Alice')
        self.assertEqual(event.confidence, 0.8)
        self.assertIn('reason', event.context)


if __name__ == '__main__':
    unittest.main()
