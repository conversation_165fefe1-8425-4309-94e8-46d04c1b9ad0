"""
Performance tests for Meeting Moderator Bot

Tests the performance characteristics, scalability, and resource usage
of the Meeting Moderator Bot components.
"""

import unittest
import time
import asyncio
import threading
import psutil
import os
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, patch

from ..core.moderator import MeetingModerator
from ..core.topic_classifier import TopicClassifier
from ..core.suggestion_engine import SuggestionE<PERSON><PERSON>, SuggestionConfig
from ..core.learning_system import LearningSystem, LearningConfig
from ..integrations.base_integration import RateLimiter
from ..utils.config import ModeratorConfig


class TestPerformanceBenchmarks(unittest.TestCase):
    """Performance benchmark tests"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = ModeratorConfig()
        self.config.classification.use_ai_classification = False  # Use rule-based for consistent timing
        self.api_key = "test_api_key"
        
        # Initialize components
        self.moderator = MeetingModerator(self.config, self.api_key, "perf_test_team")
        
    def test_message_processing_speed(self):
        """Test message processing speed"""
        # Test messages of varying complexity
        test_messages = [
            "Yesterday I worked on the login feature.",  # Simple standup update
            "I'm blocked on the database migration because the staging environment is down and we need to coordinate with the DevOps team to get it back up.",  # Complex blocker
            "Did you guys see the game last night? It was amazing! The quarterback threw for 300 yards and three touchdowns.",  # Off-topic
            "The performance issue we're seeing is related to the N+1 query problem in our ORM. We need to implement eager loading for the user relationships and add proper indexing on the foreign key columns.",  # Technical deep-dive
        ]
        
        # Warm up
        for _ in range(5):
            self.moderator.process_speech("WarmupUser", "Warmup message")
        
        # Benchmark processing speed
        start_time = time.time()
        
        for i, message in enumerate(test_messages * 10):  # Process each message 10 times
            events = self.moderator.process_speech(f"User{i % 4}", message)
        
        end_time = time.time()
        
        total_messages = len(test_messages) * 10
        processing_time = end_time - start_time
        messages_per_second = total_messages / processing_time
        
        print(f"\nMessage Processing Performance:")
        print(f"  Total messages: {total_messages}")
        print(f"  Processing time: {processing_time:.3f} seconds")
        print(f"  Messages per second: {messages_per_second:.2f}")
        print(f"  Average time per message: {(processing_time / total_messages) * 1000:.2f} ms")
        
        # Performance assertions
        self.assertLess(processing_time / total_messages, 0.1)  # Less than 100ms per message
        self.assertGreater(messages_per_second, 10)  # At least 10 messages per second
    
    def test_topic_classification_performance(self):
        """Test topic classification performance"""
        classifier = TopicClassifier(self.api_key, self.config.classification)
        
        test_texts = [
            "Yesterday I completed the user authentication module.",
            "The database performance is terrible, we need to optimize queries.",
            "Did anyone watch the movie last night?",
            "I think we should redesign the entire architecture.",
            "I'm blocked on getting API access from the third-party service.",
        ]
        
        # Benchmark classification speed
        start_time = time.time()
        
        for _ in range(100):  # Classify each text 20 times
            for text in test_texts:
                classification = classifier.classify_text(text, "individual_updates")
        
        end_time = time.time()
        
        total_classifications = len(test_texts) * 100
        processing_time = end_time - start_time
        classifications_per_second = total_classifications / processing_time
        
        print(f"\nTopic Classification Performance:")
        print(f"  Total classifications: {total_classifications}")
        print(f"  Processing time: {processing_time:.3f} seconds")
        print(f"  Classifications per second: {classifications_per_second:.2f}")
        print(f"  Average time per classification: {(processing_time / total_classifications) * 1000:.2f} ms")
        
        # Performance assertions
        self.assertLess(processing_time / total_classifications, 0.05)  # Less than 50ms per classification
        self.assertGreater(classifications_per_second, 20)  # At least 20 classifications per second
    
    def test_suggestion_engine_performance(self):
        """Test suggestion engine performance"""
        suggestion_config = SuggestionConfig(enable_ai_suggestions=False)
        suggestion_engine = SuggestionEngine(self.api_key, suggestion_config)
        
        # Mock classification and conversation state
        mock_classification = Mock()
        mock_classification.is_standup_relevant = False
        mock_classification.topic_type = 'off_topic_discussion'
        mock_classification.confidence = 0.8
        mock_classification.intent_analysis = None
        mock_classification.semantic_analysis = None
        
        mock_conversation_state = Mock()
        mock_conversation_state.total_duration = 600.0
        mock_conversation_state.current_speaker_duration = 60.0
        mock_conversation_state.speaking_pattern = {"Alice": 120.0, "Bob": 180.0}
        mock_conversation_state.participants_spoken = ["Alice", "Bob"]
        
        # Benchmark suggestion generation
        start_time = time.time()
        
        for _ in range(200):
            suggestions = suggestion_engine.generate_suggestions(
                mock_classification, mock_conversation_state, "individual_updates", "Alice"
            )
        
        end_time = time.time()
        
        processing_time = end_time - start_time
        suggestions_per_second = 200 / processing_time
        
        print(f"\nSuggestion Engine Performance:")
        print(f"  Total suggestion generations: 200")
        print(f"  Processing time: {processing_time:.3f} seconds")
        print(f"  Suggestions per second: {suggestions_per_second:.2f}")
        print(f"  Average time per generation: {(processing_time / 200) * 1000:.2f} ms")
        
        # Performance assertions
        self.assertLess(processing_time / 200, 0.02)  # Less than 20ms per suggestion generation
        self.assertGreater(suggestions_per_second, 50)  # At least 50 generations per second
    
    def test_memory_usage(self):
        """Test memory usage during operation"""
        process = psutil.Process(os.getpid())
        
        # Get initial memory usage
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create multiple moderators and process messages
        moderators = []
        for i in range(10):
            moderator = MeetingModerator(self.config, self.api_key, f"memory_test_team_{i}")
            moderator.start_meeting([f"User{j}" for j in range(5)])
            moderators.append(moderator)
        
        # Process many messages
        for moderator in moderators:
            for i in range(100):
                moderator.process_speech(f"User{i % 5}", f"Test message number {i}")
        
        # Get peak memory usage
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        print(f"\nMemory Usage:")
        print(f"  Initial memory: {initial_memory:.2f} MB")
        print(f"  Peak memory: {peak_memory:.2f} MB")
        print(f"  Memory increase: {memory_increase:.2f} MB")
        print(f"  Memory per moderator: {memory_increase / 10:.2f} MB")
        
        # Memory assertions
        self.assertLess(memory_increase, 500)  # Less than 500MB increase
        self.assertLess(memory_increase / 10, 50)  # Less than 50MB per moderator


class TestConcurrencyAndScalability(unittest.TestCase):
    """Test concurrency and scalability"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = ModeratorConfig()
        self.config.classification.use_ai_classification = False
        self.api_key = "test_api_key"
    
    def test_concurrent_message_processing(self):
        """Test processing messages concurrently"""
        moderator = MeetingModerator(self.config, self.api_key, "concurrent_test_team")
        moderator.start_meeting(["Alice", "Bob", "Charlie", "David"])
        
        def process_messages(user_id, message_count):
            """Process messages for a specific user"""
            results = []
            for i in range(message_count):
                events = moderator.process_speech(f"User{user_id}", f"Message {i} from user {user_id}")
                results.append(len(events))
            return results
        
        # Test concurrent processing with multiple threads
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            for user_id in range(4):
                future = executor.submit(process_messages, user_id, 50)
                futures.append(future)
            
            # Wait for all threads to complete
            results = [future.result() for future in futures]
        
        end_time = time.time()
        
        total_messages = 4 * 50
        processing_time = end_time - start_time
        messages_per_second = total_messages / processing_time
        
        print(f"\nConcurrent Processing Performance:")
        print(f"  Total messages: {total_messages}")
        print(f"  Processing time: {processing_time:.3f} seconds")
        print(f"  Messages per second: {messages_per_second:.2f}")
        print(f"  Threads: 4")
        
        # Verify all messages were processed
        total_events = sum(sum(result) for result in results)
        self.assertGreaterEqual(total_events, 0)  # Should have some events
        
        # Performance assertions
        self.assertGreater(messages_per_second, 20)  # Should handle at least 20 messages/sec concurrently
    
    def test_multiple_moderators_scalability(self):
        """Test scalability with multiple moderators"""
        moderator_count = 20
        messages_per_moderator = 25
        
        def create_and_run_moderator(moderator_id):
            """Create a moderator and process messages"""
            moderator = MeetingModerator(self.config, self.api_key, f"scale_test_team_{moderator_id}")
            moderator.start_meeting([f"User{i}" for i in range(3)])
            
            event_count = 0
            for i in range(messages_per_moderator):
                events = moderator.process_speech(f"User{i % 3}", f"Scalability test message {i}")
                event_count += len(events)
            
            return event_count
        
        # Test with multiple moderators
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for moderator_id in range(moderator_count):
                future = executor.submit(create_and_run_moderator, moderator_id)
                futures.append(future)
            
            # Wait for all moderators to complete
            results = [future.result() for future in futures]
        
        end_time = time.time()
        
        total_messages = moderator_count * messages_per_moderator
        processing_time = end_time - start_time
        messages_per_second = total_messages / processing_time
        
        print(f"\nScalability Test:")
        print(f"  Moderators: {moderator_count}")
        print(f"  Messages per moderator: {messages_per_moderator}")
        print(f"  Total messages: {total_messages}")
        print(f"  Processing time: {processing_time:.3f} seconds")
        print(f"  Messages per second: {messages_per_second:.2f}")
        print(f"  Total events generated: {sum(results)}")
        
        # Performance assertions
        self.assertLess(processing_time, 30)  # Should complete within 30 seconds
        self.assertGreater(messages_per_second, 15)  # Should maintain reasonable throughput
    
    def test_rate_limiter_performance(self):
        """Test rate limiter performance under load"""
        rate_limiter = RateLimiter({'default': 100, 'high': 1000})
        
        # Test rate limiter performance
        start_time = time.time()
        
        allowed_requests = 0
        denied_requests = 0
        
        # Simulate high load
        for i in range(10000):
            user_id = f"user_{i % 100}"  # 100 different users
            limit_type = 'high' if i % 10 == 0 else 'default'
            
            if rate_limiter.allow_request(user_id, limit_type):
                allowed_requests += 1
            else:
                denied_requests += 1
        
        end_time = time.time()
        
        processing_time = end_time - start_time
        requests_per_second = 10000 / processing_time
        
        print(f"\nRate Limiter Performance:")
        print(f"  Total requests: 10000")
        print(f"  Allowed requests: {allowed_requests}")
        print(f"  Denied requests: {denied_requests}")
        print(f"  Processing time: {processing_time:.3f} seconds")
        print(f"  Requests per second: {requests_per_second:.2f}")
        
        # Performance assertions
        self.assertGreater(requests_per_second, 1000)  # Should handle >1000 requests/sec
        self.assertGreater(allowed_requests, 0)  # Should allow some requests
        self.assertGreater(denied_requests, 0)  # Should deny some requests (rate limiting working)


class TestResourceUsage(unittest.TestCase):
    """Test resource usage patterns"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config = ModeratorConfig()
        self.config.classification.use_ai_classification = False
        self.api_key = "test_api_key"
    
    def test_learning_system_performance(self):
        """Test learning system performance with large datasets"""
        learning_config = LearningConfig(enable_learning=True)
        learning_system = LearningSystem(learning_config)
        
        # Generate large amount of feedback data
        start_time = time.time()
        
        for i in range(1000):
            learning_system.collect_feedback(
                feedback_type=learning_system.FeedbackType.OVERALL_MEETING,
                rating=learning_system.FeedbackRating.POSITIVE,
                participant=f"User{i % 50}",  # 50 different users
                team_id=f"team_{i % 10}",     # 10 different teams
                context={"test_data": i}
            )
        
        # Test analytics generation
        analytics_start = time.time()
        analytics = learning_system.get_learning_analytics()
        analytics_time = time.time() - analytics_start
        
        # Test team analysis
        analysis_start = time.time()
        for team_id in range(10):
            analysis = learning_system.analyze_team_patterns(f"team_{team_id}")
        analysis_time = time.time() - analysis_start
        
        end_time = time.time()
        
        total_time = end_time - start_time
        feedback_per_second = 1000 / total_time
        
        print(f"\nLearning System Performance:")
        print(f"  Feedback entries: 1000")
        print(f"  Teams: 10")
        print(f"  Total time: {total_time:.3f} seconds")
        print(f"  Feedback per second: {feedback_per_second:.2f}")
        print(f"  Analytics generation time: {analytics_time * 1000:.2f} ms")
        print(f"  Team analysis time: {analysis_time * 1000:.2f} ms")
        
        # Performance assertions
        self.assertLess(total_time, 5)  # Should complete within 5 seconds
        self.assertLess(analytics_time, 0.1)  # Analytics should be fast
        self.assertLess(analysis_time, 1.0)  # Analysis should complete within 1 second
        
        # Verify data integrity
        self.assertEqual(len(learning_system.feedback_entries), 1000)
        self.assertEqual(len(learning_system.team_profiles), 10)
    
    def test_long_running_meeting_performance(self):
        """Test performance during long-running meetings"""
        moderator = MeetingModerator(self.config, self.api_key, "long_meeting_team")
        moderator.start_meeting(["Alice", "Bob", "Charlie"])
        
        # Simulate a very long meeting with many messages
        start_time = time.time()
        
        for i in range(1000):  # 1000 messages
            participant = ["Alice", "Bob", "Charlie"][i % 3]
            message = f"Long meeting message number {i}"
            
            events = moderator.process_speech(participant, message)
        
        # Get final statistics
        stats = moderator.get_statistics()
        end_time = time.time()
        
        processing_time = end_time - start_time
        messages_per_second = 1000 / processing_time
        
        print(f"\nLong Meeting Performance:")
        print(f"  Messages processed: 1000")
        print(f"  Processing time: {processing_time:.3f} seconds")
        print(f"  Messages per second: {messages_per_second:.2f}")
        print(f"  Total interruptions: {stats['total_interruptions']}")
        print(f"  Effectiveness score: {stats['effectiveness_score']:.3f}")
        
        # Performance assertions
        self.assertLess(processing_time, 60)  # Should complete within 1 minute
        self.assertGreater(messages_per_second, 15)  # Should maintain good throughput
        
        # Verify meeting state is still consistent
        self.assertIsInstance(stats['effectiveness_score'], float)
        self.assertGreaterEqual(stats['total_interruptions'], 0)


if __name__ == '__main__':
    # Run performance tests
    print("Running Meeting Moderator Bot Performance Tests")
    print("=" * 60)
    
    unittest.main(verbosity=2)
