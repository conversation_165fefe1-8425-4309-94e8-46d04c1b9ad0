"""
Meeting Moderator Bot Module

AI-powered meeting moderator that ensures productive standup meetings
by preventing off-topic discussions and technical deep-dives.

This module is designed as a separate service that can be integrated
into Microsoft Teams, Slack, or other meeting platforms.
"""

from .core.moderator import MeetingModerator
from .core.topic_classifier import TopicClassifier
from .core.interruption_engine import InterruptionEngine
from .core.conversation_monitor import ConversationMonitor
from .integrations.teams_bot import TeamsBot
from .utils.config import ModeratorConfig

__version__ = "1.0.0"
__author__ = "Daily Meeting Emulator Team"

__all__ = [
    'MeetingModerator',
    'TopicClassifier', 
    'InterruptionEngine',
    'ConversationMonitor',
    'TeamsBot',
    'ModeratorConfig'
]
