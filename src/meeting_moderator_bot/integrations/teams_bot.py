"""
Microsoft Teams Bot Integration

Integrates the Meeting Moderator Bot with Microsoft Teams using the Bot Framework.
Handles Teams-specific message formatting, authentication, and meeting events.
"""

import logging
import asyncio
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

# Teams Bot Framework imports (these would need to be installed)
try:
    from botbuilder.core import ActivityH<PERSON><PERSON>, TurnContext, MessageFactory
    from botbuilder.schema import ChannelAccount, Activity, ActivityTypes
    TEAMS_AVAILABLE = True
except ImportError:
    # Mock classes for development without Teams SDK
    class ActivityHandler:
        pass
    class TurnContext:
        pass
    class MessageFactory:
        @staticmethod
        def text(text: str):
            return {'text': text}
    class ChannelAccount:
        pass
    class Activity:
        pass
    class ActivityTypes:
        message = "message"
    TEAMS_AVAILABLE = False

from .base_integration import (
    BaseIntegration, PlatformMessage, PlatformResponse,
    IntegrationConfig, PlatformType, MessageType
)
from ..core.moderator import MeetingModerator, ModerationEvent
from ..utils.config import ModeratorConfig

logger = logging.getLogger(__name__)


@dataclass
class TeamsMessage:
    """Teams message wrapper"""
    sender: str
    text: str
    timestamp: float
    message_id: str
    conversation_id: str


@dataclass
class TeamsConfig(IntegrationConfig):
    """Configuration for Teams integration"""

    # Teams-specific settings
    app_id: Optional[str] = None
    app_password: Optional[str] = None
    tenant_id: Optional[str] = None

    # Bot behavior
    auto_respond_to_mentions: bool = True
    send_welcome_message: bool = True
    send_meeting_summaries: bool = True

    # Override parent field with default
    platform_type: PlatformType = PlatformType.TEAMS


class TeamsBot(BaseIntegration, ActivityHandler):
    """
    Microsoft Teams Bot for Meeting Moderation

    Integrates the Meeting Moderator with Microsoft Teams to provide
    real-time meeting moderation in Teams meetings and channels.
    """

    def __init__(self, config: TeamsConfig, moderator_config: ModeratorConfig, gemini_api_key: str):
        """
        Initialize Teams Bot

        Args:
            config: Teams-specific configuration
            moderator_config: Moderator configuration
            gemini_api_key: API key for Gemini AI services
        """
        # Initialize base integration
        BaseIntegration.__init__(self, config, moderator_config, gemini_api_key)

        # Initialize ActivityHandler if Teams SDK is available
        if TEAMS_AVAILABLE:
            ActivityHandler.__init__(self)

        self.teams_config = config

        # Teams-specific state
        self.active_conversations = {}  # conversation_id -> context
        self.bot_app_id = config.app_id

        # Message formatting
        self.message_templates = self._setup_message_templates()

        logger.info("Teams Bot initialized")
    
    def _setup_message_templates(self) -> Dict[str, str]:
        """Setup Teams-specific message templates"""
        
        personality = self.config.bot_personality
        
        if personality == "professional":
            return {
                'welcome': "Hello! I'm here to help keep our standup meeting focused and efficient. Please share your daily updates.",
                'interruption_gentle': "Excuse me, {participant}. Could we keep this brief and focus on standup updates?",
                'interruption_firm': "Let's get back to our standup format. We can discuss this in detail after the meeting.",
                'technical_redirect': "This sounds like a technical discussion. Should we schedule a separate session for this?",
                'time_warning': "We're running over time. Let's wrap up the remaining updates quickly.",
                'meeting_end': "Thank you everyone! The standup is complete. Please follow up on any blockers mentioned."
            }
        elif personality == "friendly":
            return {
                'welcome': "Hi everyone! 😊 I'm here to help keep our standup on track. Let's share our updates!",
                'interruption_gentle': "Hey {participant}, could we keep this focused on standup updates? Thanks!",
                'interruption_firm': "Let's circle back to our standup format - we can chat about this afterwards!",
                'technical_redirect': "Ooh, interesting technical stuff! Should we park this for a tech discussion later?",
                'time_warning': "Time check! ⏰ Let's wrap up the remaining updates.",
                'meeting_end': "Great standup everyone! 👏 Don't forget to follow up on those blockers."
            }
        else:  # assertive
            return {
                'welcome': "Good morning. I will be moderating this standup to ensure we stay on schedule and on topic.",
                'interruption_gentle': "{participant}, please focus on your standup update.",
                'interruption_firm': "We need to return to the standup format immediately.",
                'technical_redirect': "Technical discussions belong in a separate meeting. Please note this as a blocker.",
                'time_warning': "Time limit exceeded. Conclude your updates now.",
                'meeting_end': "Standup complete. Address blockers in follow-up meetings."
            }
    
    async def connect(self) -> bool:
        """
        Connect to Teams (for Teams, this is handled by the Bot Framework)

        Returns:
            True if Teams SDK is available, False otherwise
        """
        if not TEAMS_AVAILABLE:
            logger.error("Teams SDK not available")
            return False

        self.health_status["status"] = "connected"
        self.health_status["last_check"] = time.time()
        logger.info("Teams bot connected")
        return True

    async def disconnect(self) -> None:
        """Disconnect from Teams"""
        self.health_status["status"] = "disconnected"
        logger.info("Teams bot disconnected")

    async def send_message(self, response: PlatformResponse, context: Dict[str, Any]) -> bool:
        """
        Send a message to Teams

        Args:
            response: Response to send
            context: Context with turn_context or conversation info

        Returns:
            True if message sent successfully, False otherwise
        """
        try:
            turn_context = context.get('turn_context')
            if not turn_context:
                logger.warning("No turn_context provided for Teams message")
                return False

            # Format message for Teams
            formatted_message = self._format_teams_message(response.message, response)

            if response.is_private:
                # Send private message
                await turn_context.send_activity(MessageFactory.text(formatted_message))
            else:
                # Send to channel/conversation
                await turn_context.send_activity(MessageFactory.text(formatted_message))

            return True

        except Exception as e:
            logger.error(f"Failed to send Teams message: {e}")
            return False

    async def join_meeting(self, meeting_id: str) -> bool:
        """
        Join a Teams meeting

        Args:
            meeting_id: Teams meeting identifier

        Returns:
            True if joined successfully, False otherwise
        """
        # For Teams bots, joining is typically automatic when invited
        logger.info(f"Teams bot joining meeting {meeting_id}")
        return True

    async def leave_meeting(self, meeting_id: str) -> None:
        """
        Leave a Teams meeting

        Args:
            meeting_id: Teams meeting identifier
        """
        logger.info(f"Teams bot leaving meeting {meeting_id}")

        # Clean up meeting state
        if meeting_id in self.active_moderators:
            del self.active_moderators[meeting_id]

    async def on_message_activity(self, turn_context: TurnContext) -> None:
        """
        Handle incoming messages from Teams

        Args:
            turn_context: Teams turn context with message information
        """
        try:
            # Convert Teams message to standard format
            platform_message = self._convert_teams_message(turn_context)

            # Skip bot's own messages
            if platform_message.is_bot_message:
                return

            # Process message using base integration
            responses = await self.process_message(platform_message)

            # Send responses back to Teams
            for response in responses:
                await self.send_message(response, {'turn_context': turn_context})

        except Exception as e:
            logger.error(f"Error processing Teams message: {e}")
            await self._send_error_message(turn_context)
    
    async def on_members_added_activity(
        self, members_added: List[ChannelAccount], turn_context: TurnContext
    ) -> None:
        """
        Handle new members joining the conversation
        
        Args:
            members_added: List of new members
            turn_context: Teams turn context
        """
        for member in members_added:
            if member.id != turn_context.activity.recipient.id:
                # New participant joined
                welcome_message = self.message_templates['welcome']
                await turn_context.send_activity(MessageFactory.text(welcome_message))
                
                # Start meeting if not already started
                conversation_id = turn_context.activity.conversation.id
                if conversation_id not in self.active_meetings:
                    await self._start_meeting(turn_context)
    
    def _convert_teams_message(self, turn_context: TurnContext) -> PlatformMessage:
        """Convert Teams message to standard PlatformMessage format"""

        activity = turn_context.activity

        return PlatformMessage(
            message_id=activity.id or f"teams_{int(time.time())}",
            sender=activity.from_property.name or "Unknown",
            content=activity.text or "",
            timestamp=activity.timestamp.timestamp() if activity.timestamp else time.time(),
            message_type=MessageType.TEXT,
            platform_data={
                'teams_activity_type': activity.type,
                'teams_channel_id': activity.channel_id,
                'teams_service_url': activity.service_url,
                'teams_conversation': activity.conversation.id if activity.conversation else None,
                'teams_from_id': activity.from_property.id if activity.from_property else None
            },
            meeting_id=None,  # Teams doesn't directly provide meeting ID in messages
            channel_id=activity.conversation.id if activity.conversation else None,
            thread_id=None,
            is_bot_message=self._is_bot_message(turn_context),
            mentions=self._extract_mentions(activity),
            attachments=self._extract_attachments(activity)
        )

    def _extract_teams_message(self, turn_context: TurnContext) -> TeamsMessage:
        """Extract message information from Teams context"""
        
        activity = turn_context.activity
        
        return TeamsMessage(
            sender=activity.from_property.name or "Unknown",
            text=activity.text or "",
            timestamp=activity.timestamp.timestamp() if activity.timestamp else 0.0,
            message_id=activity.id or "",
            conversation_id=activity.conversation.id or ""
        )
    
    def _extract_mentions(self, activity) -> List[str]:
        """Extract mentions from Teams activity"""
        mentions = []
        if hasattr(activity, 'entities') and activity.entities:
            for entity in activity.entities:
                if hasattr(entity, 'type') and entity.type == 'mention':
                    if hasattr(entity, 'mentioned') and hasattr(entity.mentioned, 'name'):
                        mentions.append(entity.mentioned.name)
        return mentions

    def _extract_attachments(self, activity) -> List[Dict[str, Any]]:
        """Extract attachments from Teams activity"""
        attachments = []
        if hasattr(activity, 'attachments') and activity.attachments:
            for attachment in activity.attachments:
                attachments.append({
                    'content_type': getattr(attachment, 'content_type', 'unknown'),
                    'name': getattr(attachment, 'name', 'unnamed'),
                    'content_url': getattr(attachment, 'content_url', None)
                })
        return attachments

    def _is_bot_message(self, turn_context: TurnContext) -> bool:
        """Check if message is from the bot itself"""

        activity = turn_context.activity
        return (activity.from_property.id == turn_context.activity.recipient.id or
                activity.from_property.name == self.moderator_config.bot_name)
    
    def _get_or_create_meeting(self, conversation_id: str) -> Dict:
        """Get existing meeting or create new one"""
        
        if conversation_id not in self.active_meetings:
            self.active_meetings[conversation_id] = {
                'started': False,
                'participants': set(),
                'message_count': 0,
                'last_activity': 0.0
            }
        
        return self.active_meetings[conversation_id]
    
    async def _start_meeting(self, turn_context: TurnContext):
        """Start a new meeting session"""
        
        conversation_id = turn_context.activity.conversation.id
        
        # Initialize meeting in moderator
        participants = list(self.active_meetings[conversation_id]['participants'])
        self.moderator.start_meeting(participants)
        
        # Mark meeting as started
        self.active_meetings[conversation_id]['started'] = True
        
        logger.info(f"Started meeting moderation for conversation {conversation_id}")
    
    async def _handle_moderation_event(self, turn_context: TurnContext, event: ModerationEvent):
        """Handle a moderation event from the moderator"""
        
        if event.event_type == 'interruption':
            await self._send_interruption_message(turn_context, event)
        elif event.event_type == 'suggestion':
            await self._send_suggestion_message(turn_context, event)
        elif event.event_type == 'phase_change':
            await self._send_phase_change_message(turn_context, event)
    
    async def _send_interruption_message(self, turn_context: TurnContext, event: ModerationEvent):
        """Send an interruption message to Teams"""
        
        # Format message based on confidence and context
        if event.confidence > 0.8:
            # High confidence - direct interruption
            message = event.message
        else:
            # Lower confidence - gentler approach
            message = f"Just a gentle reminder: {event.message}"
        
        # Add Teams-specific formatting
        formatted_message = self._format_teams_message(message, event)
        
        await turn_context.send_activity(MessageFactory.text(formatted_message))
        
        logger.info(f"Sent interruption message: {event.reason}")
    
    async def _send_suggestion_message(self, turn_context: TurnContext, event: ModerationEvent):
        """Send a suggestion message to Teams"""
        
        # Format as adaptive card or rich message if possible
        formatted_message = self._format_teams_message(event.message, event)
        
        await turn_context.send_activity(MessageFactory.text(formatted_message))
    
    async def _send_phase_change_message(self, turn_context: TurnContext, event: ModerationEvent):
        """Send a phase change message to Teams"""
        
        # Use bold formatting for phase changes
        formatted_message = f"**{event.message}**"
        
        await turn_context.send_activity(MessageFactory.text(formatted_message))
    
    def _format_teams_message(self, message: str, event: ModerationEvent) -> str:
        """Format message for Teams with appropriate styling"""
        
        # Add emoji based on event type and personality
        if self.config.bot_personality == "friendly":
            if event.event_type == 'interruption':
                return f"🤔 {message}"
            elif event.event_type == 'suggestion':
                return f"💡 {message}"
            else:
                return f"📋 {message}"
        elif self.config.bot_personality == "professional":
            return f"ℹ️ {message}"
        else:  # assertive
            return f"⚠️ {message}"
    
    async def _send_error_message(self, turn_context: TurnContext):
        """Send error message when something goes wrong"""
        
        error_message = "I'm having trouble processing that message. Please continue with your standup."
        await turn_context.send_activity(MessageFactory.text(error_message))
    
    def _update_meeting_state(self, conversation_id: str, message: TeamsMessage):
        """Update meeting state with new message"""
        
        if conversation_id in self.active_meetings:
            meeting_state = self.active_meetings[conversation_id]
            meeting_state['participants'].add(message.sender)
            meeting_state['message_count'] += 1
            meeting_state['last_activity'] = message.timestamp
    
    async def end_meeting(self, turn_context: TurnContext) -> Dict[str, Any]:
        """End the current meeting and return summary"""
        
        conversation_id = turn_context.activity.conversation.id
        
        # Get meeting summary from moderator
        summary = self.moderator.end_meeting()
        
        # Send completion message
        completion_message = self.message_templates['meeting_end']
        await turn_context.send_activity(MessageFactory.text(completion_message))
        
        # Clean up meeting state
        if conversation_id in self.active_meetings:
            del self.active_meetings[conversation_id]
        
        logger.info(f"Ended meeting for conversation {conversation_id}")
        
        return summary
    
    def get_meeting_statistics(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """Get current meeting statistics"""
        
        if conversation_id in self.active_meetings:
            return self.moderator.get_statistics()
        return None
    
    def is_teams_available(self) -> bool:
        """Check if Teams SDK is available"""
        return TEAMS_AVAILABLE
