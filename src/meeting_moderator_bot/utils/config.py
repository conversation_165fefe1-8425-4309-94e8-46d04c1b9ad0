"""
Configuration management for Meeting Moderator Bot

Handles loading and validation of configuration settings for all components.
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
import yaml
import os

from ..core.topic_classifier import ClassificationConfig
from ..core.interruption_engine import InterruptionConfig
from ..core.conversation_monitor import MonitoringConfig

logger = logging.getLogger(__name__)


@dataclass
class ModeratorConfig:
    """Main configuration for Meeting Moderator Bot"""
    
    # Component configurations
    classification: ClassificationConfig = field(default_factory=ClassificationConfig)
    interruption: InterruptionConfig = field(default_factory=InterruptionConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    
    # General settings
    bot_name: str = "Meeting Moderator"
    bot_personality: str = "professional"  # professional, friendly, assertive
    language: str = "en"
    
    # Meeting settings
    default_meeting_duration: float = 900.0  # 15 minutes
    max_meeting_duration: float = 1800.0  # 30 minutes
    standup_format: str = "standard"  # standard, scrum, custom
    
    # Integration settings
    enable_teams_integration: bool = False
    enable_slack_integration: bool = False
    enable_webhook_integration: bool = True
    
    # Logging and monitoring
    log_level: str = "INFO"
    enable_analytics: bool = True
    analytics_endpoint: Optional[str] = None
    
    # AI settings
    gemini_model: str = "gemini-2.5-flash"
    ai_confidence_threshold: float = 0.7
    enable_learning: bool = True
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ModeratorConfig':
        """Create configuration from dictionary"""
        
        # Extract component configurations
        classification_config = ClassificationConfig()
        if 'classification' in config_dict:
            classification_data = config_dict['classification']
            classification_config = ClassificationConfig(
                confidence_threshold=classification_data.get('confidence_threshold', 0.7),
                technical_keywords_weight=classification_data.get('technical_keywords_weight', 0.3),
                context_window_size=classification_data.get('context_window_size', 5),
                use_ai_classification=classification_data.get('use_ai_classification', True),
                gemini_model=classification_data.get('gemini_model', "gemini-2.5-flash")
            )
        
        interruption_config = InterruptionConfig()
        if 'interruption' in config_dict:
            interruption_data = config_dict['interruption']
            interruption_config = InterruptionConfig(
                confidence_threshold=interruption_data.get('confidence_threshold', 0.7),
                max_off_topic_duration=interruption_data.get('max_off_topic_duration', 30.0),
                max_technical_duration=interruption_data.get('max_technical_duration', 45.0),
                escalation_time=interruption_data.get('escalation_time', 15.0),
                participant_speaking_limit=interruption_data.get('participant_speaking_limit', 120.0),
                politeness_factor=interruption_data.get('politeness_factor', 0.8)
            )
        
        monitoring_config = MonitoringConfig()
        if 'monitoring' in config_dict:
            monitoring_data = config_dict['monitoring']
            monitoring_config = MonitoringConfig(
                context_window_size=monitoring_data.get('context_window_size', 10),
                max_context_length=monitoring_data.get('max_context_length', 1000),
                speaking_time_threshold=monitoring_data.get('speaking_time_threshold', 120.0),
                engagement_tracking=monitoring_data.get('engagement_tracking', True),
                topic_extraction=monitoring_data.get('topic_extraction', True)
            )
        
        # Create main config
        return cls(
            classification=classification_config,
            interruption=interruption_config,
            monitoring=monitoring_config,
            bot_name=config_dict.get('bot_name', "Meeting Moderator"),
            bot_personality=config_dict.get('bot_personality', "professional"),
            language=config_dict.get('language', "en"),
            default_meeting_duration=config_dict.get('default_meeting_duration', 900.0),
            max_meeting_duration=config_dict.get('max_meeting_duration', 1800.0),
            standup_format=config_dict.get('standup_format', "standard"),
            enable_teams_integration=config_dict.get('enable_teams_integration', False),
            enable_slack_integration=config_dict.get('enable_slack_integration', False),
            enable_webhook_integration=config_dict.get('enable_webhook_integration', True),
            log_level=config_dict.get('log_level', "INFO"),
            enable_analytics=config_dict.get('enable_analytics', True),
            analytics_endpoint=config_dict.get('analytics_endpoint'),
            gemini_model=config_dict.get('gemini_model', "gemini-2.5-flash"),
            ai_confidence_threshold=config_dict.get('ai_confidence_threshold', 0.7),
            enable_learning=config_dict.get('enable_learning', True)
        )
    
    @classmethod
    def from_yaml_file(cls, file_path: str) -> 'ModeratorConfig':
        """Load configuration from YAML file"""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            
            logger.info(f"Loaded configuration from {file_path}")
            return cls.from_dict(config_dict)
            
        except FileNotFoundError:
            logger.warning(f"Configuration file {file_path} not found, using defaults")
            return cls()
        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration: {e}")
            return cls()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return cls()
    
    @classmethod
    def from_environment(cls) -> 'ModeratorConfig':
        """Load configuration from environment variables"""
        
        config_dict = {}
        
        # General settings
        if os.getenv('MODERATOR_BOT_NAME'):
            config_dict['bot_name'] = os.getenv('MODERATOR_BOT_NAME')
        if os.getenv('MODERATOR_BOT_PERSONALITY'):
            config_dict['bot_personality'] = os.getenv('MODERATOR_BOT_PERSONALITY')
        if os.getenv('MODERATOR_LANGUAGE'):
            config_dict['language'] = os.getenv('MODERATOR_LANGUAGE')
        
        # Meeting settings
        if os.getenv('MODERATOR_DEFAULT_DURATION'):
            config_dict['default_meeting_duration'] = float(os.getenv('MODERATOR_DEFAULT_DURATION'))
        if os.getenv('MODERATOR_MAX_DURATION'):
            config_dict['max_meeting_duration'] = float(os.getenv('MODERATOR_MAX_DURATION'))
        
        # Integration settings
        if os.getenv('MODERATOR_ENABLE_TEAMS'):
            config_dict['enable_teams_integration'] = os.getenv('MODERATOR_ENABLE_TEAMS').lower() == 'true'
        if os.getenv('MODERATOR_ENABLE_SLACK'):
            config_dict['enable_slack_integration'] = os.getenv('MODERATOR_ENABLE_SLACK').lower() == 'true'
        
        # AI settings
        if os.getenv('MODERATOR_GEMINI_MODEL'):
            config_dict['gemini_model'] = os.getenv('MODERATOR_GEMINI_MODEL')
        if os.getenv('MODERATOR_AI_CONFIDENCE'):
            config_dict['ai_confidence_threshold'] = float(os.getenv('MODERATOR_AI_CONFIDENCE'))
        
        # Component-specific settings
        classification_config = {}
        if os.getenv('MODERATOR_CLASSIFICATION_CONFIDENCE'):
            classification_config['confidence_threshold'] = float(os.getenv('MODERATOR_CLASSIFICATION_CONFIDENCE'))
        if os.getenv('MODERATOR_USE_AI_CLASSIFICATION'):
            classification_config['use_ai_classification'] = os.getenv('MODERATOR_USE_AI_CLASSIFICATION').lower() == 'true'
        if classification_config:
            config_dict['classification'] = classification_config
        
        interruption_config = {}
        if os.getenv('MODERATOR_MAX_OFF_TOPIC_DURATION'):
            interruption_config['max_off_topic_duration'] = float(os.getenv('MODERATOR_MAX_OFF_TOPIC_DURATION'))
        if os.getenv('MODERATOR_SPEAKING_LIMIT'):
            interruption_config['participant_speaking_limit'] = float(os.getenv('MODERATOR_SPEAKING_LIMIT'))
        if os.getenv('MODERATOR_POLITENESS_FACTOR'):
            interruption_config['politeness_factor'] = float(os.getenv('MODERATOR_POLITENESS_FACTOR'))
        if interruption_config:
            config_dict['interruption'] = interruption_config
        
        logger.info("Loaded configuration from environment variables")
        return cls.from_dict(config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        
        return {
            'bot_name': self.bot_name,
            'bot_personality': self.bot_personality,
            'language': self.language,
            'default_meeting_duration': self.default_meeting_duration,
            'max_meeting_duration': self.max_meeting_duration,
            'standup_format': self.standup_format,
            'enable_teams_integration': self.enable_teams_integration,
            'enable_slack_integration': self.enable_slack_integration,
            'enable_webhook_integration': self.enable_webhook_integration,
            'log_level': self.log_level,
            'enable_analytics': self.enable_analytics,
            'analytics_endpoint': self.analytics_endpoint,
            'gemini_model': self.gemini_model,
            'ai_confidence_threshold': self.ai_confidence_threshold,
            'enable_learning': self.enable_learning,
            'classification': {
                'confidence_threshold': self.classification.confidence_threshold,
                'technical_keywords_weight': self.classification.technical_keywords_weight,
                'context_window_size': self.classification.context_window_size,
                'use_ai_classification': self.classification.use_ai_classification,
                'gemini_model': self.classification.gemini_model
            },
            'interruption': {
                'confidence_threshold': self.interruption.confidence_threshold,
                'max_off_topic_duration': self.interruption.max_off_topic_duration,
                'max_technical_duration': self.interruption.max_technical_duration,
                'escalation_time': self.interruption.escalation_time,
                'participant_speaking_limit': self.interruption.participant_speaking_limit,
                'politeness_factor': self.interruption.politeness_factor
            },
            'monitoring': {
                'context_window_size': self.monitoring.context_window_size,
                'max_context_length': self.monitoring.max_context_length,
                'speaking_time_threshold': self.monitoring.speaking_time_threshold,
                'engagement_tracking': self.monitoring.engagement_tracking,
                'topic_extraction': self.monitoring.topic_extraction
            }
        }
    
    def save_to_yaml_file(self, file_path: str):
        """Save configuration to YAML file"""
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.to_dict(), f, default_flow_style=False, indent=2)
            
            logger.info(f"Configuration saved to {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration to {file_path}: {e}")
            raise
    
    def validate(self) -> tuple[bool, list[str]]:
        """Validate configuration settings"""
        
        errors = []
        
        # Validate durations
        if self.default_meeting_duration <= 0:
            errors.append("default_meeting_duration must be positive")
        if self.max_meeting_duration <= self.default_meeting_duration:
            errors.append("max_meeting_duration must be greater than default_meeting_duration")
        
        # Validate confidence thresholds
        if not 0.0 <= self.ai_confidence_threshold <= 1.0:
            errors.append("ai_confidence_threshold must be between 0.0 and 1.0")
        if not 0.0 <= self.classification.confidence_threshold <= 1.0:
            errors.append("classification.confidence_threshold must be between 0.0 and 1.0")
        if not 0.0 <= self.interruption.confidence_threshold <= 1.0:
            errors.append("interruption.confidence_threshold must be between 0.0 and 1.0")
        
        # Validate personality
        valid_personalities = ['professional', 'friendly', 'assertive']
        if self.bot_personality not in valid_personalities:
            errors.append(f"bot_personality must be one of {valid_personalities}")
        
        # Validate standup format
        valid_formats = ['standard', 'scrum', 'custom']
        if self.standup_format not in valid_formats:
            errors.append(f"standup_format must be one of {valid_formats}")
        
        # Validate log level
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self.log_level not in valid_log_levels:
            errors.append(f"log_level must be one of {valid_log_levels}")
        
        return len(errors) == 0, errors
