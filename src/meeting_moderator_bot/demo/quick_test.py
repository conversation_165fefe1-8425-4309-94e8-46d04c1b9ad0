#!/usr/bin/env python3
"""
Quick Test of Meeting Moderator Bot

Быстрый тест основных функций бота для проверки работоспособности.
"""

import os
import sys
import time
import logging

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from src.meeting_moderator_bot.core.moderator import MeetingModerator
from src.meeting_moderator_bot.utils.config import ModeratorConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_basic_functionality():
    """Тест базовой функциональности"""
    
    print("🧪 БЫСТРЫЙ ТЕСТ MEETING MODERATOR BOT")
    print("="*50)
    
    # Конфигурация
    config = ModeratorConfig()
    api_key = os.getenv('GEMINI_API_KEY', 'test_key')
    
    if api_key == 'test_key':
        print("⚠️ GEMINI_API_KEY не установлен. Используется rule-based режим.")
        config.classification.use_ai_classification = False
    else:
        print(f"✅ Используется {config.gemini_model} для AI анализа")
    
    print(f"\n📊 Конфигурация:")
    print(f"   AI Model: {config.gemini_model}")
    print(f"   Classification Model: {config.classification.gemini_model}")
    print(f"   AI Classification: {config.classification.use_ai_classification}")
    
    # Создаем модератор
    moderator = MeetingModerator(config, api_key, "test_team")
    
    # Тестовые сценарии
    test_scenarios = [
        {
            "name": "Нормальный standup",
            "messages": [
                ("Алиса", "Вчера работала над логином. Сегодня добавлю тесты. Блокеров нет."),
                ("Боб", "Завершил API. Сегодня займусь документацией. Нужен доступ к staging."),
            ]
        },
        {
            "name": "Off-topic дискуссия",
            "messages": [
                ("Алиса", "Вчера работала над фичей."),
                ("Боб", "Кстати, кто смотрел матч вчера? Было круто!"),
                ("Чарли", "Да! Невероятная игра!"),
            ]
        },
        {
            "name": "Техническое обсуждение",
            "messages": [
                ("Алиса", "У меня проблемы с производительностью базы данных."),
                ("Боб", "Нужно добавить индексы и оптимизировать запросы. Также стоит рассмотреть кэширование."),
            ]
        }
    ]
    
    # Запускаем тесты
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🎭 Тест {i}: {scenario['name']}")
        print("-" * 40)
        
        # Начинаем встречу
        participants = ["Алиса", "Боб", "Чарли"]
        moderator.start_meeting(participants)
        
        total_events = 0
        total_time = 0
        
        # Обрабатываем сообщения
        for speaker, message in scenario['messages']:
            print(f"\n👤 {speaker}: {message}")
            
            start_time = time.time()
            events = moderator.process_speech(speaker, message)
            processing_time = time.time() - start_time
            
            total_events += len(events)
            total_time += processing_time
            
            if events:
                for event in events:
                    icon = "🛑" if event.event_type == "interruption" else "💡"
                    print(f"   {icon} Бот: {event.message}")
                    print(f"      Тип: {event.event_type}, Уверенность: {event.confidence:.2f}")
            else:
                print(f"   ✅ Без вмешательств")
            
            print(f"   ⏱️ {processing_time*1000:.1f}ms")
        
        # Завершаем встречу
        summary = moderator.end_meeting()
        
        print(f"\n📊 Результаты:")
        print(f"   События: {total_events}")
        print(f"   Время обработки: {total_time*1000:.1f}ms")
        print(f"   Эффективность: {summary['effectiveness_score']:.2f}")
        print(f"   Прерывания: {summary['total_interruptions']}")
    
    # Общая статистика
    stats = moderator.get_statistics()
    print(f"\n📈 ОБЩАЯ СТАТИСТИКА:")
    print(f"   Модель: {config.gemini_model}")
    
    if 'performance_metrics' in stats:
        perf = stats['performance_metrics']
        print(f"   Всего запросов: {perf.get('total_requests', 0)}")
        print(f"   Среднее время: {perf.get('average_response_time', 0)*1000:.1f}ms")
        print(f"   Запросов/сек: {perf.get('requests_per_second', 0):.1f}")
    
    if 'cache_statistics' in stats:
        cache = stats['cache_statistics']
        print(f"   Кэш статистика:")
        for cache_type, cache_stats in cache.items():
            if cache_stats:
                print(f"     {cache_type}: {cache_stats.get('hit_rate', 0):.1%} hit rate")
    
    print(f"\n✅ Тест завершен успешно!")


def test_performance():
    """Тест производительности"""
    
    print(f"\n🚀 ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ")
    print("="*30)
    
    config = ModeratorConfig()
    api_key = os.getenv('GEMINI_API_KEY', 'test_key')
    
    if api_key == 'test_key':
        config.classification.use_ai_classification = False
    
    moderator = MeetingModerator(config, api_key, "perf_test_team")
    moderator.start_meeting(["User1", "User2", "User3"])
    
    # Тестовые сообщения
    messages = [
        "Вчера работал над фичей",
        "Сегодня буду тестировать",
        "У меня есть блокер",
        "Кто смотрел футбол?",
        "Нужно обсудить архитектуру",
    ] * 10  # 50 сообщений
    
    print(f"Обработка {len(messages)} сообщений...")
    
    start_time = time.time()
    total_events = 0
    
    for i, message in enumerate(messages):
        speaker = f"User{(i % 3) + 1}"
        events = moderator.process_speech(speaker, message)
        total_events += len(events)
        
        if (i + 1) % 10 == 0:
            print(f"  Обработано {i + 1}/{len(messages)}")
    
    total_time = time.time() - start_time
    
    print(f"\n📊 Результаты производительности:")
    print(f"   Сообщений: {len(messages)}")
    print(f"   Общее время: {total_time:.2f}s")
    print(f"   Время на сообщение: {(total_time/len(messages))*1000:.1f}ms")
    print(f"   Сообщений в секунду: {len(messages)/total_time:.1f}")
    print(f"   Всего событий: {total_events}")
    
    # Оценка
    avg_time = total_time / len(messages)
    if avg_time < 0.05:
        print(f"   🚀 Отличная производительность!")
    elif avg_time < 0.1:
        print(f"   ✅ Хорошая производительность")
    else:
        print(f"   ⚠️ Производительность можно улучшить")


def test_ai_features():
    """Тест AI функций (если доступен API ключ)"""
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print(f"\n⚠️ GEMINI_API_KEY не установлен. Пропускаем AI тесты.")
        return
    
    print(f"\n🤖 ТЕСТ AI ФУНКЦИЙ")
    print("="*25)
    
    config = ModeratorConfig()
    config.classification.use_ai_classification = True
    
    print(f"Используется модель: {config.gemini_model}")
    
    moderator = MeetingModerator(config, api_key, "ai_test_team")
    moderator.start_meeting(["Алиса", "Боб"])
    
    # Тестовые сообщения для AI анализа
    ai_test_messages = [
        ("Алиса", "Вчера я работала над новой функцией аутентификации пользователей"),
        ("Боб", "Кстати, а кто-нибудь смотрел новый фильм Marvel? Он просто потрясающий!"),
        ("Алиса", "Мне нужно обсудить сложную архитектуру микросервисов и их взаимодействие через API Gateway"),
    ]
    
    for speaker, message in ai_test_messages:
        print(f"\n👤 {speaker}: {message}")
        
        start_time = time.time()
        events = moderator.process_speech(speaker, message)
        processing_time = time.time() - start_time
        
        if events:
            for event in events:
                print(f"   🤖 AI Анализ: {event.message}")
                print(f"      Тип: {event.event_type}")
                print(f"      Уверенность: {event.confidence:.2f}")
                if event.context:
                    print(f"      Контекст: {event.context}")
        else:
            print(f"   ✅ AI не обнаружил проблем")
        
        print(f"   ⏱️ AI обработка: {processing_time*1000:.1f}ms")
    
    print(f"\n✅ AI тесты завершены!")


def main():
    """Главная функция"""
    
    print("🤖 MEETING MODERATOR BOT - QUICK TEST")
    print("🚀 Powered by Gemini 2.5 Flash")
    print("="*50)
    
    try:
        # Основные тесты
        test_basic_functionality()
        
        # Тест производительности
        test_performance()
        
        # AI тесты (если доступен ключ)
        test_ai_features()
        
        print(f"\n🎉 ВСЕ ТЕСТЫ ЗАВЕРШЕНЫ УСПЕШНО!")
        print(f"\nДля полной демонстрации запустите:")
        print(f"python demo/live_demo.py")
        
    except Exception as e:
        print(f"\n❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
