#!/usr/bin/env python3
"""
Live Demo of Meeting Moderator Bot with Gemini 2.5 Flash

Интерактивная демонстрация всех возможностей бота в реальном времени.
"""

import os
import sys
import time
import logging
from typing import List, Dict, Any

# Add the project root to the path so we can import our modules
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from src.meeting_moderator_bot.core.moderator import MeetingModerator
from src.meeting_moderator_bot.utils.config import ModeratorConfig
from src.meeting_moderator_bot.integrations.webhook_integration import WebhookIntegration, WebhookConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LiveDemo:
    """
    Интерактивная демонстрация Meeting Moderator Bot
    
    Показывает работу всех компонентов в реальном времени
    с использованием Gemini 2.5 Flash.
    """
    
    def __init__(self):
        """Инициализация демо"""
        self.config = ModeratorConfig()
        self.api_key = os.getenv('GEMINI_API_KEY', 'demo_key')
        
        # Проверяем наличие API ключа
        if self.api_key == 'demo_key':
            logger.warning("⚠️ GEMINI_API_KEY не установлен. Используется rule-based режим.")
            self.config.classification.use_ai_classification = False
            self.ai_enabled = False
        else:
            logger.info(f"✅ Используется Gemini 2.5 Flash для AI анализа")
            self.ai_enabled = True
        
        # Инициализируем модератор
        self.moderator = MeetingModerator(self.config, self.api_key, "demo_team")
        
        # Демо сценарии
        self.demo_scenarios = self._create_demo_scenarios()
        
    def run_interactive_demo(self):
        """Запуск интерактивной демонстрации"""
        
        print("\n" + "="*80)
        print("🤖 MEETING MODERATOR BOT - LIVE DEMO")
        print("🚀 Powered by Gemini 2.5 Flash")
        print("="*80)
        
        print(f"\n📊 Конфигурация:")
        print(f"   AI Model: {self.config.gemini_model}")
        print(f"   AI Enabled: {'✅ Да' if self.ai_enabled else '❌ Нет (rule-based)'}")
        print(f"   Classification Model: {self.config.classification.gemini_model}")
        print(f"   Team ID: demo_team")
        
        while True:
            print(f"\n🎭 Выберите демо сценарий:")
            print(f"1. 🏃‍♂️ Обычный standup (baseline)")
            print(f"2. 🎮 Off-topic дискуссия")
            print(f"3. 🔧 Техническое обсуждение")
            print(f"4. ⏰ Долгая встреча")
            print(f"5. 🎯 Смешанные проблемы")
            print(f"6. 📝 Интерактивный режим")
            print(f"7. 📊 Показать статистику")
            print(f"8. 🧪 Тест производительности")
            print(f"0. 🚪 Выход")
            
            choice = input(f"\n👉 Ваш выбор (0-8): ").strip()
            
            if choice == '0':
                print(f"\n👋 До свидания!")
                break
            elif choice == '1':
                self._run_scenario("baseline")
            elif choice == '2':
                self._run_scenario("off_topic")
            elif choice == '3':
                self._run_scenario("technical")
            elif choice == '4':
                self._run_scenario("long_meeting")
            elif choice == '5':
                self._run_scenario("mixed_issues")
            elif choice == '6':
                self._run_interactive_mode()
            elif choice == '7':
                self._show_statistics()
            elif choice == '8':
                self._run_performance_test()
            else:
                print(f"❌ Неверный выбор. Попробуйте снова.")
    
    def _create_demo_scenarios(self) -> Dict[str, Dict]:
        """Создание демо сценариев"""
        
        return {
            "baseline": {
                "name": "🏃‍♂️ Обычный Standup",
                "description": "Хорошо структурированная встреча с правильными обновлениями",
                "participants": ["Алиса", "Боб", "Чарли"],
                "messages": [
                    ("Алиса", "Вчера я завершила модуль аутентификации. Сегодня буду работать над сбросом пароля. Блокеров нет."),
                    ("Боб", "Я закончил миграцию базы данных вчера. Сегодня начинаю работу над API endpoints. Заблокирован на доступе к staging среде."),
                    ("Чарли", "Вчера работал над frontend компонентами. Сегодня буду интегрировать их с API Боба. Пока блокеров нет."),
                ]
            },
            "off_topic": {
                "name": "🎮 Off-topic дискуссия",
                "description": "Встреча с отклонениями от темы, требующими перенаправления",
                "participants": ["Алиса", "Боб", "Чарли"],
                "messages": [
                    ("Алиса", "Вчера работала над функцией логина. Сегодня добавлю тесты."),
                    ("Боб", "Кстати, вы смотрели матч вчера? Это было невероятно! Квотербек набрал 400 ярдов."),
                    ("Чарли", "Да! И тот тачдаун в последнюю минуту был потрясающим! Не могу поверить, что они выиграли."),
                    ("Алиса", "Ладно, вернемся к рабочим обновлениям. Я также заблокирована на документации API."),
                ]
            },
            "technical": {
                "name": "🔧 Техническое обсуждение",
                "description": "Встреча с техническими дискуссиями, которые нужно перенести",
                "participants": ["Алиса", "Боб", "Дэвид"],
                "messages": [
                    ("Алиса", "Вчера работала над пользовательским сервисом. Сегодня продолжу с тестированием."),
                    ("Боб", "У меня проблемы с производительностью запросов к базе данных. Проблема N+1 вызывает таймауты."),
                    ("Дэвид", "Нам нужно реализовать eager loading и добавить правильную индексацию. Также стоит рассмотреть оптимизацию запросов и возможно внедрить кэширование с Redis."),
                    ("Боб", "Верно, и нам также нужно посмотреть на конфигурацию connection pooling и возможно переключиться на другую ORM."),
                ]
            },
            "long_meeting": {
                "name": "⏰ Долгая встреча",
                "description": "Встреча, которая затягивается и требует управления временем",
                "participants": ["Алиса", "Боб", "Чарли", "Дэвид"],
                "messages": [
                    ("Алиса", "Вчера работала над множественными функциями включая аутентификацию, авторизацию и управление пользователями. Сегодня продолжу с системой управления профилями и также буду работать над страницей настроек."),
                    ("Боб", "Я провел вчера отлаживая интеграцию платежей, что заняло больше времени чем ожидалось. Были проблемы с обработкой webhook'ов и также некоторые проблемы с логикой конвертации валют."),
                    ("Чарли", "Я работал над редизайном frontend'а и также помогал с мобильным приложением. Есть некоторые несоответствия в дизайне которые нужно решить и также проблемы с производительностью на старых устройствах."),
                    ("Дэвид", "Я работал над настройкой инфраструктуры включая конфигурацию Docker, развертывание Kubernetes, CI/CD pipeline, а также настройку мониторинга и систем логирования."),
                ]
            },
            "mixed_issues": {
                "name": "🎯 Смешанные проблемы",
                "description": "Встреча с различными типами проблем, требующими разных вмешательств",
                "participants": ["Алиса", "Боб", "Чарли"],
                "messages": [
                    ("Алиса", "Вчера работала над аутентификацией пользователей. Сегодня добавлю тесты."),
                    ("Боб", "Кто-нибудь смотрел новый сериал на Netflix? Он действительно хорош!"),
                    ("Чарли", "Насчет аутентификации, нам нужно обсудить детали реализации JWT и стратегию обновления токенов."),
                    ("Алиса", "Я работаю над этим уже три дня и есть так много крайних случаев для рассмотрения. Одна только валидация пароля имеет пятнадцать различных правил и затем есть интеграция двухфакторной аутентификации."),
                ]
            }
        }
    
    def _run_scenario(self, scenario_key: str):
        """Запуск конкретного сценария"""
        
        scenario = self.demo_scenarios[scenario_key]
        
        print(f"\n" + "="*60)
        print(f"🎭 {scenario['name']}")
        print(f"📝 {scenario['description']}")
        print(f"👥 Участники: {', '.join(scenario['participants'])}")
        print("="*60)
        
        # Начинаем встречу
        self.moderator.start_meeting(scenario['participants'])
        print(f"\n🚀 Встреча началась!")
        
        # Обрабатываем сообщения
        for i, (speaker, message) in enumerate(scenario['messages'], 1):
            print(f"\n📢 {i}. {speaker}: {message}")
            
            # Небольшая пауза для реализма
            time.sleep(1)
            
            # Обрабатываем речь
            start_time = time.time()
            events = self.moderator.process_speech(speaker, message)
            processing_time = time.time() - start_time
            
            # Показываем результаты
            if events:
                for event in events:
                    icon = self._get_event_icon(event.event_type)
                    print(f"   {icon} Бот ({event.event_type}): {event.message}")
                    print(f"      Уверенность: {event.confidence:.2f}")
                    if event.context:
                        print(f"      Контекст: {event.context}")
            else:
                print(f"   ✅ Сообщение обработано без вмешательств")
            
            print(f"   ⏱️ Время обработки: {processing_time*1000:.1f}ms")
        
        # Завершаем встречу
        summary = self.moderator.end_meeting()
        self._show_meeting_summary(summary)
        
        # Предлагаем оставить отзыв
        self._collect_feedback()
    
    def _run_interactive_mode(self):
        """Интерактивный режим для ввода собственных сообщений"""
        
        print(f"\n" + "="*60)
        print(f"📝 ИНТЕРАКТИВНЫЙ РЕЖИМ")
        print(f"Введите сообщения участников встречи. Бот будет анализировать их в реальном времени.")
        print(f"Команды: 'start' - начать встречу, 'end' - завершить, 'quit' - выйти")
        print("="*60)
        
        meeting_started = False
        participants = []
        
        while True:
            if not meeting_started:
                command = input(f"\n👉 Введите 'start' для начала встречи или 'quit' для выхода: ").strip().lower()
                
                if command == 'quit':
                    break
                elif command == 'start':
                    participants_input = input(f"👥 Введите имена участников через запятую: ").strip()
                    participants = [name.strip() for name in participants_input.split(',') if name.strip()]
                    
                    if participants:
                        self.moderator.start_meeting(participants)
                        meeting_started = True
                        print(f"🚀 Встреча началась с участниками: {', '.join(participants)}")
                    else:
                        print(f"❌ Нужно указать хотя бы одного участника")
                else:
                    print(f"❌ Неизвестная команда")
            else:
                user_input = input(f"\n👤 Введите сообщение (Имя: Сообщение) или 'end'/'quit': ").strip()
                
                if user_input.lower() in ['end', 'quit']:
                    if user_input.lower() == 'end':
                        summary = self.moderator.end_meeting()
                        self._show_meeting_summary(summary)
                        meeting_started = False
                    else:
                        break
                elif ':' in user_input:
                    try:
                        speaker, message = user_input.split(':', 1)
                        speaker = speaker.strip()
                        message = message.strip()
                        
                        if speaker and message:
                            print(f"\n📢 {speaker}: {message}")
                            
                            start_time = time.time()
                            events = self.moderator.process_speech(speaker, message)
                            processing_time = time.time() - start_time
                            
                            if events:
                                for event in events:
                                    icon = self._get_event_icon(event.event_type)
                                    print(f"   {icon} Бот: {event.message}")
                                    print(f"      Уверенность: {event.confidence:.2f}")
                            else:
                                print(f"   ✅ Сообщение обработано без вмешательств")
                            
                            print(f"   ⏱️ Время обработки: {processing_time*1000:.1f}ms")
                        else:
                            print(f"❌ Неверный формат. Используйте: Имя: Сообщение")
                    except Exception as e:
                        print(f"❌ Ошибка обработки: {e}")
                else:
                    print(f"❌ Неверный формат. Используйте: Имя: Сообщение")
    
    def _show_statistics(self):
        """Показать статистику работы бота"""
        
        stats = self.moderator.get_statistics()
        
        print(f"\n" + "="*60)
        print(f"📊 СТАТИСТИКА РАБОТЫ БОТА")
        print("="*60)
        
        print(f"\n🎯 Основные метрики:")
        print(f"   Общая длительность: {stats.get('current_duration', 0)/60:.1f} минут")
        print(f"   Всего прерываний: {stats.get('total_interruptions', 0)}")
        print(f"   Off-topic сегментов: {stats.get('off_topic_segments', 0)}")
        print(f"   Оценка эффективности: {stats.get('effectiveness_score', 0):.2f}")
        print(f"   Текущая фаза: {stats.get('current_phase', 'N/A')}")
        
        if 'participants' in stats:
            print(f"\n👥 Участники:")
            for name, info in stats['participants'].items():
                print(f"   {name}: {info.get('speaking_time', 0):.1f}s говорения")
        
        if 'performance_metrics' in stats:
            perf = stats['performance_metrics']
            print(f"\n⚡ Производительность:")
            print(f"   Всего запросов: {perf.get('total_requests', 0)}")
            print(f"   Среднее время отклика: {perf.get('average_response_time', 0)*1000:.1f}ms")
            print(f"   Запросов в секунду: {perf.get('requests_per_second', 0):.1f}")
            print(f"   Использование памяти: {perf.get('memory_usage_mb', 0):.1f}MB")
        
        if 'cache_statistics' in stats:
            cache = stats['cache_statistics']
            print(f"\n💾 Кэширование:")
            for cache_type, cache_stats in cache.items():
                if cache_stats:
                    print(f"   {cache_type}: {cache_stats.get('hit_rate', 0):.1%} hit rate")
    
    def _run_performance_test(self):
        """Тест производительности"""
        
        print(f"\n" + "="*60)
        print(f"🧪 ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ")
        print("="*60)
        
        test_messages = [
            ("Алиса", "Вчера работала над функцией логина"),
            ("Боб", "Сегодня буду тестировать API"),
            ("Чарли", "У меня есть блокер с базой данных"),
            ("Дэвид", "Кстати, кто смотрел футбол вчера?"),
            ("Алиса", "Нам нужно обсудить архитектуру системы подробнее"),
        ]
        
        print(f"🚀 Запуск теста с {len(test_messages)} сообщениями...")
        
        # Начинаем встречу
        self.moderator.start_meeting(["Алиса", "Боб", "Чарли", "Дэвид"])
        
        total_time = 0
        total_events = 0
        
        for i, (speaker, message) in enumerate(test_messages, 1):
            start_time = time.time()
            events = self.moderator.process_speech(speaker, message)
            processing_time = time.time() - start_time
            
            total_time += processing_time
            total_events += len(events)
            
            print(f"   {i}. {processing_time*1000:.1f}ms - {len(events)} событий")
        
        # Результаты
        avg_time = total_time / len(test_messages)
        messages_per_second = len(test_messages) / total_time
        
        print(f"\n📊 Результаты:")
        print(f"   Общее время: {total_time*1000:.1f}ms")
        print(f"   Среднее время на сообщение: {avg_time*1000:.1f}ms")
        print(f"   Сообщений в секунду: {messages_per_second:.1f}")
        print(f"   Всего событий: {total_events}")
        print(f"   События на сообщение: {total_events/len(test_messages):.1f}")
        
        # Оценка производительности
        if avg_time < 0.1:
            print(f"   🚀 Отличная производительность!")
        elif avg_time < 0.2:
            print(f"   ✅ Хорошая производительность")
        else:
            print(f"   ⚠️ Производительность можно улучшить")
    
    def _show_meeting_summary(self, summary: Dict[str, Any]):
        """Показать итоги встречи"""
        
        print(f"\n📋 ИТОГИ ВСТРЕЧИ:")
        print(f"   Длительность: {summary.get('duration_minutes', 0):.1f} минут")
        print(f"   Всего прерываний: {summary.get('total_interruptions', 0)}")
        print(f"   Off-topic сегментов: {summary.get('off_topic_segments', 0)}")
        print(f"   Участников дали обновления: {summary.get('participants_who_updated', 0)}/{summary.get('total_participants', 0)}")
        print(f"   Событий модерации: {summary.get('moderation_events', 0)}")
        print(f"   Оценка эффективности: {summary.get('effectiveness_score', 0):.2f}")
        
        # Рекомендации
        effectiveness = summary.get('effectiveness_score', 0)
        if effectiveness >= 0.8:
            print(f"   🎉 Отличная встреча!")
        elif effectiveness >= 0.6:
            print(f"   ✅ Хорошая встреча")
        elif effectiveness >= 0.4:
            print(f"   ⚠️ Встреча требует улучшений")
        else:
            print(f"   ❌ Встреча была неэффективной")
    
    def _collect_feedback(self):
        """Сбор обратной связи"""
        
        print(f"\n💬 Хотите оставить отзыв о работе бота? (y/n): ", end="")
        if input().strip().lower() == 'y':
            rating = input(f"Оцените работу бота от -2 (очень плохо) до 2 (отлично): ")
            comment = input(f"Комментарий (необязательно): ")
            
            try:
                rating_int = int(rating)
                if -2 <= rating_int <= 2:
                    self.moderator.collect_feedback(
                        feedback_type="overall",
                        rating=rating_int,
                        participant="demo_user",
                        context={"demo": True},
                        comment=comment if comment else None
                    )
                    print(f"✅ Спасибо за отзыв!")
                else:
                    print(f"❌ Рейтинг должен быть от -2 до 2")
            except ValueError:
                print(f"❌ Неверный формат рейтинга")
    
    def _get_event_icon(self, event_type: str) -> str:
        """Получить иконку для типа события"""
        
        icons = {
            'interruption': '🛑',
            'suggestion': '💡',
            'phase_change': '🔄',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        return icons.get(event_type, '🤖')


def main():
    """Главная функция демо"""
    
    try:
        demo = LiveDemo()
        demo.run_interactive_demo()
    except KeyboardInterrupt:
        print(f"\n\n👋 Демо прервано пользователем. До свидания!")
    except Exception as e:
        print(f"\n❌ Ошибка в демо: {e}")
        logger.exception("Demo error")


if __name__ == "__main__":
    main()
