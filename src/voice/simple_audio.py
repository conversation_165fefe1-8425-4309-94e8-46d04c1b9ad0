"""
Simple Audio Generator

Creates a single audio file for the entire meeting using synchronous calls.
Much simpler and more reliable than async version.
"""

import os
import logging
import tempfile
from typing import Dict, List, Optional, Any
from pathlib import Path
import time

try:
    from deepgram import DeepgramClient, SpeakOptions
    from pydub import AudioSegment
    SIMPLE_AUDIO_AVAILABLE = True
except ImportError:
    SIMPLE_AUDIO_AVAILABLE = False
    DeepgramClient = None
    SpeakOptions = None
    AudioSegment = None

from rich.console import Console

logger = logging.getLogger(__name__)
console = Console()


class SimpleAudioGenerator:
    """Simple synchronous audio generator for meetings"""
    
    # Voice mapping for different participant personalities
    VOICE_MAPPING = {
        "facilitator": "aura-asteria-en",    # Clear, authoritative
        "rambler": "aura-luna-en",           # Soft, detailed  
        "questioner": "aura-stella-en",      # Curious, engaging
        "minimalist": "aura-zeus-en",        # Concise, direct
        "problem_solver": "aura-hera-en",    # Confident, solution-focused
        "storyteller": "aura-orion-en",      # Narrative, expressive
        "interrupter": "aura-arcas-en"       # Quick, energetic
    }
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize simple audio generator"""
        if not SIMPLE_AUDIO_AVAILABLE:
            raise ImportError("Simple audio requires: pip install deepgram-sdk pydub")
        
        self.api_key = api_key or os.getenv("DEEPGRAM_API_KEY")
        if not self.api_key:
            raise ValueError("Deepgram API key required for audio generation")
        
        self.client = DeepgramClient(self.api_key)
        
        # Temporary directory for audio segments
        self.temp_dir = Path(tempfile.mkdtemp(prefix="simple_audio_"))
        
        logger.info("Simple audio generator initialized")
        console.print("🎵 [green]Simple audio generator ready![/green]")
    
    def get_voice_for_participant(self, personality_type: str, participant_name: str = None) -> str:
        """Get appropriate voice for participant"""
        voice = self.VOICE_MAPPING.get(personality_type.lower(), "aura-asteria-en")
        
        # Add variety based on participant name
        if participant_name:
            name_lower = participant_name.lower()
            if "sarah" in name_lower or "emily" in name_lower:
                voice = "aura-luna-en"  # Female voice
            elif "mike" in name_lower or "david" in name_lower:
                voice = "aura-zeus-en"  # Male voice
            elif "alex" in name_lower:
                voice = "aura-asteria-en"  # Neutral facilitator voice
        
        return voice
    
    def generate_meeting_audio_sync(
        self, 
        transcript_segments: List[Dict[str, Any]], 
        output_filename: str = "meeting_complete.wav"
    ) -> str:
        """
        Generate complete meeting audio synchronously
        
        Args:
            transcript_segments: List of segments with participant, content, personality_type
            output_filename: Name of output audio file
            
        Returns:
            Path to generated audio file
        """
        try:
            console.print("🎬 [yellow]Generating complete meeting audio...[/yellow]")
            
            # Generate individual audio segments
            audio_files = []
            
            for i, segment in enumerate(transcript_segments):
                participant_name = segment.get('participant', 'Unknown')
                content = segment.get('content', '')
                personality = segment.get('personality_type', 'facilitator')
                
                if content.strip():
                    console.print(f"🎙️ [blue]Generating audio {i+1}/{len(transcript_segments)}: {participant_name}[/blue]")
                    
                    # Generate audio for this segment
                    audio_file = self._generate_segment_audio_sync(
                        text=content,
                        participant_name=participant_name,
                        personality_type=personality,
                        segment_id=i
                    )
                    
                    if audio_file:
                        audio_files.append(audio_file)
            
            console.print(f"✅ [green]Generated {len(audio_files)} audio segments[/green]")
            
            # Combine all segments into one file
            if audio_files:
                output_path = self._combine_audio_files_sync(audio_files, output_filename)
                console.print(f"🎵 [green]Complete meeting audio saved: {output_path}[/green]")
                return output_path
            else:
                console.print("❌ [red]No audio segments generated[/red]")
                return None
                
        except Exception as e:
            logger.error(f"Error generating meeting audio: {e}")
            console.print(f"❌ [red]Meeting audio generation failed: {e}[/red]")
            return None
    
    def _generate_segment_audio_sync(
        self, 
        text: str, 
        participant_name: str,
        personality_type: str,
        segment_id: int
    ) -> Optional[str]:
        """Generate audio for a single segment synchronously"""
        try:
            # Get appropriate voice
            voice = self.get_voice_for_participant(personality_type, participant_name)
            
            # Create filename
            safe_name = participant_name.replace(" ", "_")
            filename = f"segment_{segment_id:03d}_{safe_name}.wav"
            filepath = self.temp_dir / filename
            
            # Configure speech options
            options = SpeakOptions(
                model=voice,
                encoding="linear16",
                sample_rate=24000
            )
            
            # Generate speech synchronously
            response = self.client.speak.v("1").save(
                filename=str(filepath),
                source={"text": text},
                options=options
            )
            
            if filepath.exists():
                return str(filepath)
            else:
                logger.error(f"Failed to generate audio: {filepath}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating segment audio: {e}")
            return None
    
    def _combine_audio_files_sync(
        self, 
        audio_files: List[str], 
        output_filename: str
    ) -> str:
        """Combine individual audio files into one file synchronously"""
        try:
            console.print("🔧 [blue]Combining audio segments...[/blue]")
            
            # Create combined audio
            combined_audio = AudioSegment.empty()
            
            for i, file_path in enumerate(audio_files):
                console.print(f"   Adding segment {i+1}/{len(audio_files)}: {Path(file_path).name}")
                
                # Load audio segment
                audio = AudioSegment.from_wav(file_path)
                
                # Add to combined audio
                combined_audio += audio
                
                # Add pause between speakers (except for last segment)
                if i < len(audio_files) - 1:
                    # Add 1.5 second pause between speakers
                    pause_duration = 1500  # milliseconds
                    pause = AudioSegment.silent(duration=pause_duration)
                    combined_audio += pause
            
            # Save combined audio
            output_dir = Path("data/audio")
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / output_filename
            
            combined_audio.export(str(output_path), format="wav")
            
            # Clean up temporary files
            self._cleanup_temp_files(audio_files)
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error combining audio segments: {e}")
            raise
    
    def _cleanup_temp_files(self, audio_files: List[str]):
        """Clean up temporary audio files"""
        for file_path in audio_files:
            try:
                os.unlink(file_path)
            except:
                pass
    
    def cleanup(self):
        """Clean up temporary directory"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except:
            pass
    
    def is_available(self) -> bool:
        """Check if simple audio generation is available"""
        return SIMPLE_AUDIO_AVAILABLE and bool(self.api_key)


# Utility functions
def create_simple_audio_generator(api_key: str = None) -> Optional[SimpleAudioGenerator]:
    """Create and initialize simple audio generator"""
    try:
        generator = SimpleAudioGenerator(api_key)
        return generator
    except Exception as e:
        console.print(f"❌ [red]Failed to initialize simple audio generator: {e}[/red]")
        return None


def generate_meeting_audio_simple(transcript, output_filename: str = None) -> Optional[str]:
    """
    Generate complete meeting audio from transcript (synchronous)
    
    Args:
        transcript: MeetingTranscript object
        output_filename: Optional custom filename
        
    Returns:
        Path to generated audio file
    """
    try:
        generator = create_simple_audio_generator()
        if not generator:
            return None
        
        # Convert transcript to segments format
        segments = []
        for segment in transcript.segments:
            # Find participant info
            participant_info = None
            for p in transcript.participants:
                if p.name == segment.participant:
                    participant_info = p
                    break
            
            segments.append({
                'participant': segment.participant,
                'content': segment.content,
                'personality_type': participant_info.personality if participant_info else 'facilitator'
            })
        
        # Generate filename if not provided
        if not output_filename:
            timestamp = transcript.metadata.get('timestamp', 'unknown')
            output_filename = f"meeting_{timestamp}.wav"
        
        # Generate audio synchronously
        audio_path = generator.generate_meeting_audio_sync(segments, output_filename)
        
        # Cleanup
        generator.cleanup()
        
        return audio_path
        
    except Exception as e:
        logger.error(f"Error generating meeting audio from transcript: {e}")
        return None
