"""
Real-time Voice Generation and Playback

Provides real-time text-to-speech with immediate playback
for meeting participants during live simulation.
"""

import os
import asyncio
import logging
import threading
import time
from typing import Dict, Optional, Any
from pathlib import Path
import tempfile
from queue import Queue as ThreadQueue

try:
    from deepgram import DeepgramClient, SpeakOptions
    REALTIME_VOICE_AVAILABLE = True

    # Try to import audio playback libraries
    try:
        import pygame
        PYGAME_AVAILABLE = True
    except ImportError:
        PYGAME_AVAILABLE = False

    try:
        import playsound
        PLAYSOUND_AVAILABLE = True
    except ImportError:
        PLAYSOUND_AVAILABLE = False

except ImportError:
    REALTIME_VOICE_AVAILABLE = False
    DeepgramClient = None
    SpeakOptions = None
    PYGAME_AVAILABLE = False
    PLAYSOUND_AVAILABLE = False

from rich.console import Console

logger = logging.getLogger(__name__)
console = Console()


class RealTimeVoiceEngine:
    """Real-time voice generation and playback engine"""
    
    # Voice mapping for different participant personalities
    VOICE_MAPPING = {
        "facilitator": "aura-asteria-en",    # Clear, authoritative
        "rambler": "aura-luna-en",           # Soft, detailed  
        "questioner": "aura-stella-en",      # Curious, engaging
        "minimalist": "aura-zeus-en",        # Concise, direct
        "problem_solver": "aura-hera-en",    # Confident, solution-focused
        "storyteller": "aura-orion-en",      # Narrative, expressive
        "interrupter": "aura-arcas-en"       # Quick, energetic
    }
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize real-time voice engine"""
        if not REALTIME_VOICE_AVAILABLE:
            raise ImportError("Real-time voice requires: pip install deepgram-sdk pygame")
        
        self.api_key = api_key or os.getenv("DEEPGRAM_API_KEY")
        if not self.api_key:
            raise ValueError("Deepgram API key required for real-time voice")
        
        self.client = DeepgramClient(self.api_key)

        # Initialize audio playback system
        self.audio_system = self._init_audio_system()

        # Audio queue for real-time playback
        self.audio_queue = ThreadQueue()
        self.is_playing = False
        self.playback_thread = None

        # Temporary directory for audio files
        self.temp_dir = Path(tempfile.mkdtemp(prefix="meeting_voice_"))

        logger.info("Real-time voice engine initialized")
        console.print(f"🎙️ [green]Real-time voice engine ready! (Audio: {self.audio_system})[/green]")

    def _init_audio_system(self) -> str:
        """Initialize available audio playback system"""
        if PYGAME_AVAILABLE:
            try:
                import pygame
                pygame.mixer.init(frequency=24000, size=-16, channels=1, buffer=1024)
                return "pygame"
            except Exception as e:
                logger.warning(f"Pygame init failed: {e}")

        if PLAYSOUND_AVAILABLE:
            return "playsound"

        # Fallback - just generate files without playback
        console.print("⚠️ [yellow]No audio playback available - will generate files only[/yellow]")
        return "files_only"

    def get_voice_for_participant(self, personality_type: str, participant_name: str = None) -> str:
        """Get appropriate voice for participant"""
        voice = self.VOICE_MAPPING.get(personality_type.lower(), "aura-asteria-en")
        
        # Add variety based on participant name
        if participant_name:
            name_lower = participant_name.lower()
            if "sarah" in name_lower or "emily" in name_lower:
                voice = "aura-luna-en"  # Female voice
            elif "mike" in name_lower or "david" in name_lower:
                voice = "aura-zeus-en"  # Male voice
            elif "alex" in name_lower:
                voice = "aura-asteria-en"  # Neutral facilitator voice
        
        return voice
    
    def speak_immediately(
        self, 
        text: str, 
        participant_name: str,
        personality_type: str = "facilitator",
        block: bool = False
    ) -> bool:
        """
        Generate and play speech immediately
        
        Args:
            text: Text to speak
            participant_name: Name of speaker
            personality_type: Personality type for voice selection
            block: Whether to wait for speech to complete
            
        Returns:
            True if speech was queued successfully
        """
        try:
            # Get appropriate voice
            voice = self.get_voice_for_participant(personality_type, participant_name)
            
            # Generate audio in background thread
            generation_thread = threading.Thread(
                target=self._generate_and_queue_audio,
                args=(text, voice, participant_name, block)
            )
            generation_thread.daemon = True
            generation_thread.start()
            
            # Start playback thread if not running
            if not self.is_playing:
                self._start_playback_thread()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue speech for {participant_name}: {e}")
            return False
    
    def _generate_and_queue_audio(self, text: str, voice: str, participant_name: str, block: bool):
        """Generate audio and add to playback queue"""
        try:
            # Create temporary file
            audio_file = self.temp_dir / f"{participant_name}_{int(time.time() * 1000)}.wav"
            
            # Configure speech options for real-time
            options = SpeakOptions(
                model=voice,
                encoding="linear16",
                sample_rate=24000
            )
            
            # Generate speech
            console.print(f"🎙️ [blue]Generating speech for {participant_name}...[/blue]")
            
            response = self.client.speak.v("1").save(
                filename=str(audio_file),
                source={"text": text},
                options=options
            )
            
            if audio_file.exists():
                # Add to playback queue
                self.audio_queue.put({
                    'file': str(audio_file),
                    'participant': participant_name,
                    'text': text,
                    'block': block
                })
                console.print(f"🎵 [green]Audio queued for {participant_name}[/green]")
            else:
                logger.error(f"Failed to generate audio file: {audio_file}")
                
        except Exception as e:
            logger.error(f"Audio generation error for {participant_name}: {e}")
    
    def _start_playback_thread(self):
        """Start the audio playback thread"""
        if self.playback_thread and self.playback_thread.is_alive():
            return
        
        self.is_playing = True
        self.playback_thread = threading.Thread(target=self._playback_worker)
        self.playback_thread.daemon = True
        self.playback_thread.start()
    
    def _playback_worker(self):
        """Worker thread for audio playback"""
        while self.is_playing:
            try:
                # Get next audio item (with timeout)
                audio_item = self.audio_queue.get(timeout=1.0)
                
                if audio_item is None:  # Shutdown signal
                    break
                
                # Play audio file
                self._play_audio_file(audio_item)
                
                # Clean up temporary file
                try:
                    os.unlink(audio_item['file'])
                except:
                    pass
                
                self.audio_queue.task_done()
                
            except:
                # Timeout or other error - continue
                continue
        
        self.is_playing = False
    
    def _play_audio_file(self, audio_item: Dict[str, Any]):
        """Play a single audio file"""
        try:
            file_path = audio_item['file']
            participant = audio_item['participant']

            console.print(f"🔊 [yellow]{participant} is speaking...[/yellow]")

            # Play audio based on available system
            if self.audio_system == "pygame" and PYGAME_AVAILABLE:
                self._play_with_pygame(file_path)
            elif self.audio_system == "playsound" and PLAYSOUND_AVAILABLE:
                self._play_with_playsound(file_path)
            else:
                # Just show that audio was generated
                console.print(f"🎵 [blue]Audio file generated: {file_path}[/blue]")
                time.sleep(2)  # Simulate playback time

            console.print(f"✅ [green]{participant} finished speaking[/green]")

        except Exception as e:
            logger.error(f"Playback error: {e}")

    def _play_with_pygame(self, file_path: str):
        """Play audio with pygame"""
        import pygame
        pygame.mixer.music.load(file_path)
        pygame.mixer.music.play()

        # Wait for playback to complete
        while pygame.mixer.music.get_busy():
            time.sleep(0.1)

    def _play_with_playsound(self, file_path: str):
        """Play audio with playsound"""
        import playsound
        playsound.playsound(file_path)
    
    def wait_for_completion(self):
        """Wait for all queued audio to complete"""
        self.audio_queue.join()
    
    def stop(self):
        """Stop the voice engine and clean up"""
        self.is_playing = False
        
        # Signal shutdown
        self.audio_queue.put(None)
        
        # Wait for playback thread to finish
        if self.playback_thread:
            self.playback_thread.join(timeout=2.0)
        
        # Stop audio system
        if self.audio_system == "pygame" and PYGAME_AVAILABLE:
            try:
                import pygame
                pygame.mixer.quit()
            except:
                pass
        
        # Clean up temp directory
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
        except:
            pass
        
        console.print("🎙️ [blue]Voice engine stopped[/blue]")
    
    def is_available(self) -> bool:
        """Check if real-time voice is available"""
        return REALTIME_VOICE_AVAILABLE and bool(self.api_key)


# Utility functions
def create_realtime_voice_engine(api_key: str = None) -> Optional[RealTimeVoiceEngine]:
    """Create and initialize real-time voice engine"""
    try:
        engine = RealTimeVoiceEngine(api_key)
        return engine
    except Exception as e:
        console.print(f"❌ [red]Failed to initialize real-time voice: {e}[/red]")
        return None


def test_realtime_voice() -> bool:
    """Test real-time voice functionality"""
    try:
        engine = create_realtime_voice_engine()
        if not engine:
            return False
        
        # Test speech
        engine.speak_immediately(
            "Hello, this is a test of real-time voice generation.",
            "TestUser",
            "facilitator",
            block=True
        )
        
        engine.wait_for_completion()
        engine.stop()
        
        return True
        
    except Exception as e:
        logger.error(f"Real-time voice test failed: {e}")
        return False
