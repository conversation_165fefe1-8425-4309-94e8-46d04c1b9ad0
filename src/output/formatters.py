"""
Transcript Formatters

Different formatting options for meeting transcripts including
timestamp, simple, and detailed formats.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from .transcript import MeetingTranscript


class TranscriptFormatter(ABC):
    """Abstract base class for transcript formatters"""
    
    @abstractmethod
    def format(self, transcript: MeetingTranscript) -> str:
        """Format a transcript into text"""
        pass
    
    def _format_header(self, transcript: MeetingTranscript) -> str:
        """Format transcript header with metadata"""
        lines = []
        lines.append("=" * 60)
        lines.append("DAILY MEETING TRANSCRIPT")
        lines.append("=" * 60)
        
        # Meeting metadata
        metadata = transcript.metadata
        if 'generated_at' in metadata:
            lines.append(f"Generated: {metadata['generated_at']}")
        if 'style' in metadata:
            lines.append(f"Meeting Style: {metadata['style']}")
        
        lines.append(f"Duration: {transcript.duration:.1f} minutes")
        lines.append(f"Participants: {len(transcript.participants)}")
        lines.append(f"Total Segments: {len(transcript.segments)}")
        
        # Participants list
        lines.append("\nParticipants:")
        for participant in transcript.participants:
            lines.append(f"  • {participant.name} ({participant.role}) - {participant.personality}")
        
        lines.append("")
        return "\n".join(lines)
    
    def _format_footer(self, transcript: MeetingTranscript) -> str:
        """Format transcript footer with analysis"""
        lines = []
        lines.append("\n" + "=" * 60)
        lines.append("MEETING ANALYSIS")
        lines.append("=" * 60)
        
        stats = transcript.get_summary_stats()
        
        lines.append(f"Quality Score: {stats['quality_score']}/1.0")
        lines.append(f"Off-topic Segments: {stats['off_topic_segments']} ({stats['off_topic_ratio']*100:.1f}%)")
        lines.append(f"Total Words: {stats['total_words']}")
        lines.append(f"Unique Topics: {stats['unique_topics']}")
        
        # Participation breakdown
        lines.append("\nParticipation Breakdown:")
        for name, participant_stats in stats['participant_stats'].items():
            percentage = (participant_stats['words'] / stats['total_words'] * 100) if stats['total_words'] > 0 else 0
            lines.append(f"  • {name}: {participant_stats['words']} words ({percentage:.1f}%)")
        
        return "\n".join(lines)


class TimestampFormatter(TranscriptFormatter):
    """Formatter with timestamps for each segment"""
    
    def format(self, transcript: MeetingTranscript) -> str:
        """Format transcript with timestamps"""
        lines = []
        
        # Add header
        lines.append(self._format_header(transcript))
        
        # Add transcript content
        lines.append("TRANSCRIPT")
        lines.append("-" * 60)
        
        for segment in transcript.segments:
            timestamp = segment.formatted_timestamp
            participant = segment.participant
            content = segment.content
            
            # Add markers for special segments
            markers = []
            if segment.is_off_topic:
                markers.append("[OFF-TOPIC]")
            if segment.interruption:
                markers.append("[INTERRUPTION]")
            
            marker_text = " ".join(markers)
            if marker_text:
                marker_text = " " + marker_text
            
            lines.append(f"[{timestamp}] {participant}:{marker_text}")
            lines.append(f"  {content}")
            lines.append("")
        
        # Add footer
        lines.append(self._format_footer(transcript))
        
        return "\n".join(lines)


class SimpleFormatter(TranscriptFormatter):
    """Simple formatter without timestamps"""
    
    def format(self, transcript: MeetingTranscript) -> str:
        """Format transcript in simple format"""
        lines = []
        
        # Add basic header
        lines.append("DAILY MEETING TRANSCRIPT")
        lines.append("=" * 40)
        lines.append(f"Duration: {transcript.duration:.1f} minutes")
        lines.append(f"Participants: {', '.join(transcript.participant_names)}")
        lines.append("")
        
        # Add transcript content
        for segment in transcript.segments:
            participant = segment.participant
            content = segment.content
            
            lines.append(f"{participant}: {content}")
        
        # Add basic footer
        lines.append("")
        lines.append("-" * 40)
        lines.append(f"Quality Score: {transcript.quality_score:.2f}")
        lines.append(f"Off-topic: {transcript.off_topic_count}/{len(transcript.segments)} segments")
        
        return "\n".join(lines)


class DetailedFormatter(TranscriptFormatter):
    """Detailed formatter with full metadata and analysis"""
    
    def format(self, transcript: MeetingTranscript) -> str:
        """Format transcript with detailed information"""
        lines = []
        
        # Add detailed header
        lines.append(self._format_header(transcript))
        
        # Add detailed participant information
        lines.append("DETAILED PARTICIPANT INFORMATION")
        lines.append("-" * 60)
        
        for participant in transcript.participants:
            lines.append(f"\n{participant.name} ({participant.role})")
            lines.append(f"  Personality: {participant.personality}")
            lines.append(f"  Traits:")
            for trait, value in participant.traits.items():
                lines.append(f"    • {trait}: {value:.2f}")
            
            # Participation stats
            segments = transcript.get_participant_segments(participant.name)
            word_count = transcript.get_participant_word_count(participant.name)
            lines.append(f"  Participation: {len(segments)} segments, {word_count} words")
        
        lines.append("")
        
        # Add transcript content with detailed annotations
        lines.append("DETAILED TRANSCRIPT")
        lines.append("-" * 60)
        
        for i, segment in enumerate(transcript.segments):
            lines.append(f"\nSegment {i+1}:")
            lines.append(f"  Time: {segment.formatted_timestamp}")
            lines.append(f"  Speaker: {segment.participant}")
            lines.append(f"  Content: {segment.content}")
            lines.append(f"  Word Count: {segment.word_count}")
            
            # Add analysis tags
            tags = []
            if segment.is_off_topic:
                tags.append("OFF-TOPIC")
            if segment.interruption:
                tags.append("INTERRUPTION")
            if segment.topics:
                tags.append(f"Topics: {', '.join(segment.topics)}")
            
            if tags:
                lines.append(f"  Tags: {' | '.join(tags)}")
            
            # Add metadata if present
            if segment.metadata:
                lines.append(f"  Metadata: {segment.metadata}")
        
        # Add detailed footer
        lines.append(self._format_footer(transcript))
        
        # Add topic analysis
        topics = transcript.get_topics()
        if topics:
            lines.append("\nTOPIC ANALYSIS")
            lines.append("-" * 60)
            lines.append(f"Topics discussed: {', '.join(topics)}")
        
        return "\n".join(lines)


class JSONFormatter(TranscriptFormatter):
    """JSON formatter for machine-readable output"""
    
    def format(self, transcript: MeetingTranscript) -> str:
        """Format transcript as JSON"""
        return transcript.to_json()


# Formatter registry
_FORMATTERS = {
    'timestamp': TimestampFormatter,
    'simple': SimpleFormatter,
    'detailed': DetailedFormatter,
    'json': JSONFormatter,
}


def get_formatter(format_type: str) -> TranscriptFormatter:
    """
    Get a formatter instance by type
    
    Args:
        format_type: Type of formatter ('timestamp', 'simple', 'detailed', 'json')
        
    Returns:
        Formatter instance
        
    Raises:
        ValueError: If format_type is not supported
    """
    if format_type not in _FORMATTERS:
        available = ', '.join(_FORMATTERS.keys())
        raise ValueError(f"Unknown format type '{format_type}'. Available: {available}")
    
    return _FORMATTERS[format_type]()


def get_available_formats() -> list[str]:
    """Get list of available format types"""
    return list(_FORMATTERS.keys())
