"""
Meeting Transcript Classes

Data structures for representing meeting transcripts with
participants, segments, metadata, and analysis information.
"""

import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class ParticipantInfo:
    """Information about a meeting participant"""
    name: str
    role: str
    personality: str
    traits: Dict[str, float] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate participant data after initialization"""
        if not self.name:
            raise ValueError("Participant name cannot be empty")
        if not self.role:
            raise ValueError("Participant role cannot be empty")
        if not self.personality:
            raise ValueError("Participant personality cannot be empty")


@dataclass_json
@dataclass
class TranscriptSegment:
    """A single segment of meeting dialogue"""
    timestamp: float  # Seconds from meeting start
    participant: str  # Participant name
    content: str  # What was said
    is_off_topic: bool = False  # Whether this segment is off-topic
    interruption: bool = False  # Whether this was an interruption
    topics: List[str] = field(default_factory=list)  # Extracted topics
    metadata: Dict[str, Any] = field(default_factory=dict)  # Additional metadata
    
    def __post_init__(self):
        """Validate segment data after initialization"""
        if not self.participant:
            raise ValueError("Segment participant cannot be empty")
        if not self.content:
            raise ValueError("Segment content cannot be empty")
        if self.timestamp < 0:
            raise ValueError("Segment timestamp cannot be negative")
    
    @property
    def formatted_timestamp(self) -> str:
        """Get formatted timestamp as MM:SS"""
        minutes = int(self.timestamp // 60)
        seconds = int(self.timestamp % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    @property
    def word_count(self) -> int:
        """Get word count for this segment"""
        return len(self.content.split())


@dataclass_json
@dataclass
class MeetingTranscript:
    """Complete meeting transcript with metadata and analysis"""
    
    # Core data
    segments: List[TranscriptSegment] = field(default_factory=list)
    participants: List[ParticipantInfo] = field(default_factory=list)
    
    # Meeting metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Analysis data
    quality_score: float = 0.0
    off_topic_count: int = 0
    total_word_count: int = 0
    
    def __post_init__(self):
        """Initialize computed properties after creation"""
        if not self.metadata:
            self.metadata = {}
            
        # Set default metadata
        if 'timestamp' not in self.metadata:
            self.metadata['timestamp'] = datetime.now().strftime('%Y%m%d_%H%M%S')
        if 'generated_at' not in self.metadata:
            self.metadata['generated_at'] = datetime.now().isoformat()
            
        # Compute analysis if segments exist
        if self.segments:
            self._compute_analysis()
    
    def add_segment(self, segment: TranscriptSegment) -> None:
        """Add a new segment to the transcript"""
        self.segments.append(segment)
        self._update_analysis()
    
    def add_participant(self, participant: ParticipantInfo) -> None:
        """Add a new participant to the transcript"""
        # Check for duplicate names
        existing_names = [p.name for p in self.participants]
        if participant.name in existing_names:
            raise ValueError(f"Participant with name '{participant.name}' already exists")
        
        self.participants.append(participant)
    
    @property
    def duration(self) -> float:
        """Get meeting duration in minutes"""
        if not self.segments:
            return 0.0
        
        # Find the last timestamp and add estimated speaking time for last segment
        last_segment = self.segments[-1]
        estimated_speaking_time = last_segment.word_count / 150  # 150 words per minute
        total_seconds = last_segment.timestamp + (estimated_speaking_time * 60)
        
        return total_seconds / 60  # Convert to minutes
    
    @property
    def participant_names(self) -> List[str]:
        """Get list of participant names"""
        return [p.name for p in self.participants]
    
    @property
    def off_topic_ratio(self) -> float:
        """Get ratio of off-topic segments"""
        if not self.segments:
            return 0.0
        return self.off_topic_count / len(self.segments)
    
    def get_participant_segments(self, participant_name: str) -> List[TranscriptSegment]:
        """Get all segments for a specific participant"""
        return [s for s in self.segments if s.participant == participant_name]
    
    def get_participant_word_count(self, participant_name: str) -> int:
        """Get total word count for a specific participant"""
        segments = self.get_participant_segments(participant_name)
        return sum(s.word_count for s in segments)
    
    def get_topics(self) -> List[str]:
        """Get all unique topics mentioned in the meeting"""
        all_topics = []
        for segment in self.segments:
            all_topics.extend(segment.topics)
        return list(set(all_topics))
    
    def to_text(self, format_type: str = "timestamp") -> str:
        """
        Convert transcript to text format

        Args:
            format_type: Format type ('timestamp', 'simple', 'detailed')

        Returns:
            Formatted transcript as string
        """
        # Import here to avoid circular imports
        from .formatters import get_formatter

        formatter = get_formatter(format_type)
        return formatter.format(self)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert transcript to dictionary"""
        return self.to_dict()
    
    def to_json(self) -> str:
        """Convert transcript to JSON string"""
        return self.to_json()
    
    def _compute_analysis(self) -> None:
        """Compute analysis metrics for the transcript"""
        if not self.segments:
            self.off_topic_count = 0
            self.total_word_count = 0
            self.quality_score = 0.0
            return
        
        # Count off-topic segments
        self.off_topic_count = sum(1 for s in self.segments if s.is_off_topic)
        
        # Count total words
        self.total_word_count = sum(s.word_count for s in self.segments)
        
        # Calculate quality score (0.0 - 1.0)
        self.quality_score = self._calculate_quality_score()
    
    def _update_analysis(self) -> None:
        """Update analysis after adding new segments"""
        self._compute_analysis()
    
    def _calculate_quality_score(self) -> float:
        """
        Calculate meeting quality score based on various factors
        
        Returns:
            Quality score between 0.0 and 1.0
        """
        if not self.segments:
            return 0.0
        
        score = 1.0
        
        # Penalize high off-topic ratio
        off_topic_penalty = self.off_topic_ratio * 0.3
        score -= off_topic_penalty
        
        # Penalize very short or very long meetings
        duration_minutes = self.duration
        if duration_minutes < 5:
            score -= 0.2  # Too short
        elif duration_minutes > 30:
            score -= 0.1  # Too long
        
        # Reward balanced participation
        if len(self.participants) > 1:
            participant_word_counts = [
                self.get_participant_word_count(p.name) 
                for p in self.participants
            ]
            
            if max(participant_word_counts) > 0:
                # Calculate participation balance (lower is better)
                avg_words = sum(participant_word_counts) / len(participant_word_counts)
                variance = sum((count - avg_words) ** 2 for count in participant_word_counts) / len(participant_word_counts)
                balance_score = 1.0 - min(variance / (avg_words ** 2), 1.0) if avg_words > 0 else 0.0
                score = (score + balance_score) / 2
        
        # Ensure score is between 0 and 1
        return max(0.0, min(1.0, score))
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics for the meeting"""
        return {
            'duration_minutes': round(self.duration, 1),
            'total_segments': len(self.segments),
            'total_participants': len(self.participants),
            'total_words': self.total_word_count,
            'off_topic_segments': self.off_topic_count,
            'off_topic_ratio': round(self.off_topic_ratio, 2),
            'quality_score': round(self.quality_score, 2),
            'unique_topics': len(self.get_topics()),
            'participant_stats': {
                p.name: {
                    'segments': len(self.get_participant_segments(p.name)),
                    'words': self.get_participant_word_count(p.name),
                    'role': p.role,
                    'personality': p.personality
                }
                for p in self.participants
            }
        }
