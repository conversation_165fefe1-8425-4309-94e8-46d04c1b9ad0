"""
Simple Participant Implementation

Basic implementation of BaseParticipant for testing and
simple meeting simulations.
"""

from typing import Dict, List
from .base_participant import (
    BaseParticipant, PersonalityTraits, ConversationContext,
    Role, PersonalityType
)


class SimpleParticipant(BaseParticipant):
    """
    Simple implementation of BaseParticipant
    
    Provides basic functionality for meeting participation
    without complex AI-driven behavior.
    """
    
    def _initialize_response_patterns(self) -> Dict[str, List[str]]:
        """Initialize basic response patterns"""
        base_patterns = {
            'opening': [
                "Good morning everyone!",
                "Hi team, ready for standup.",
                "Morning all!"
            ],
            'update': [
                "Yesterday I worked on {task}. Today I'll continue with {next_task}.",
                "I completed {task} yesterday and will focus on {next_task} today.",
                "Working on {task}, making good progress."
            ],
            'blocker': [
                "I'm blocked on {issue} and need help.",
                "Having trouble with {issue}, could use some guidance.",
                "Stuck on {issue}, anyone have experience with this?"
            ],
            'question': [
                "Quick question about {topic}?",
                "Can someone clarify {topic}?",
                "I'm not sure about {topic}, can you explain?"
            ],
            'closing': [
                "Thanks everyone!",
                "Have a great day!",
                "See you all later!"
            ]
        }

        # Add facilitator-specific patterns
        if self.personality_type == PersonalityType.FACILITATOR:
            base_patterns['opening'] = [
                "Good morning everyone! Let's start our daily standup.",
                "Hi team, let's go around and share our updates.",
                "Morning all! Who would like to start today?"
            ]
            base_patterns['closing'] = [
                "Thanks everyone, that's all for today's standup.",
                "Great updates everyone! Have a productive day.",
                "Thanks for the updates. Let's connect offline for any blockers."
            ]

        return base_patterns
    
    def generate_response(self, context: ConversationContext, prompt: str) -> str:
        """
        Generate a simple response based on context
        
        Args:
            context: Current conversation context
            prompt: Specific prompt for response
            
        Returns:
            Generated response text
        """
        phase = context.meeting_phase
        
        if phase in self.response_patterns:
            patterns = self.response_patterns[phase]
            if patterns:
                import random
                base_response = random.choice(patterns)
                
                # Simple template substitution
                if '{task}' in base_response:
                    base_response = base_response.replace('{task}', self._get_sample_task())
                if '{next_task}' in base_response:
                    base_response = base_response.replace('{next_task}', self._get_sample_task())
                if '{issue}' in base_response:
                    base_response = base_response.replace('{issue}', self._get_sample_issue())
                if '{topic}' in base_response:
                    base_response = base_response.replace('{topic}', self._get_sample_topic())
                
                return base_response
        
        # Fallback response with more realistic content
        if phase == "updates":
            return self._generate_realistic_update()
        elif phase == "opening":
            return "Good morning everyone!"
        elif phase == "closing":
            return "Thanks everyone!"
        else:
            return self._generate_realistic_update()
    
    def should_interrupt(self, context: ConversationContext) -> bool:
        """
        Determine if participant should interrupt
        
        Args:
            context: Current conversation context
            
        Returns:
            True if should interrupt, False otherwise
        """
        # Simple logic based on interruption tendency
        import random
        base_probability = self.personality_traits.interruption_tendency
        
        # Reduce probability if already interrupted recently
        if self.interruption_count > 2:
            base_probability *= 0.5
        
        return random.random() < base_probability
    
    def should_ask_question(self, context: ConversationContext) -> bool:
        """
        Determine if participant should ask a question
        
        Args:
            context: Current conversation context
            
        Returns:
            True if should ask question, False otherwise
        """
        import random
        
        # Base probability from curiosity trait
        base_probability = self.personality_traits.curiosity * 0.3
        
        # Increase if someone mentioned something technical
        if context.previous_statements:
            last_statement = context.previous_statements[-1].lower()
            technical_keywords = ['api', 'database', 'bug', 'error', 'issue']
            if any(keyword in last_statement for keyword in technical_keywords):
                base_probability *= 1.5
        
        return random.random() < base_probability
    
    def _calculate_response_length(self, base_length: int, context: ConversationContext) -> int:
        """
        Calculate response length based on personality
        
        Args:
            base_length: Base word count
            context: Current conversation context
            
        Returns:
            Adjusted word count
        """
        # Adjust based on verbosity
        length_multiplier = 0.5 + (self.personality_traits.verbosity * 1.0)
        adjusted_length = int(base_length * length_multiplier)
        
        # Ensure reasonable bounds
        return max(5, min(adjusted_length, 100))
    
    def _adjust_speaking_duration(self, duration: float, content: str) -> float:
        """
        Adjust speaking duration based on personality
        
        Args:
            duration: Base duration in seconds
            content: Content being spoken
            
        Returns:
            Adjusted duration in seconds
        """
        # Adjust based on speaking speed
        speed_multiplier = self.personality_traits.speaking_speed
        adjusted_duration = duration / speed_multiplier
        
        # Add pauses based on pause frequency
        pause_time = duration * self.personality_traits.pause_frequency * 0.2
        
        return adjusted_duration + pause_time
    
    def _get_sample_task(self) -> str:
        """Get a sample task description"""
        tasks = [
            "the user authentication feature",
            "database optimization",
            "frontend component updates",
            "API endpoint testing",
            "bug fixes in the payment module",
            "code review for the new feature",
            "documentation updates",
            "performance improvements"
        ]
        
        import random
        return random.choice(tasks)
    
    def _get_sample_issue(self) -> str:
        """Get a sample issue description"""
        issues = [
            "the API integration",
            "database connection issues",
            "test environment setup",
            "third-party service configuration",
            "deployment pipeline",
            "authentication flow",
            "data migration",
            "performance bottleneck"
        ]
        
        import random
        return random.choice(issues)
    
    def _get_sample_topic(self) -> str:
        """Get a sample topic for questions"""
        topics = [
            "the deployment process",
            "the new API changes",
            "the database schema",
            "the testing strategy",
            "the security requirements",
            "the performance metrics",
            "the integration approach",
            "the timeline"
        ]

        import random
        return random.choice(topics)

    def _generate_realistic_update(self) -> str:
        """Generate a realistic standup update based on role and personality"""
        import random

        # Role-specific tasks
        role_tasks = {
            "scrum_master": [
                "facilitating sprint planning sessions",
                "removing blockers for the team",
                "updating stakeholders on our progress",
                "preparing for the retrospective meeting",
                "coordinating with the product owner",
                "tracking sprint metrics and velocity"
            ],
            "senior_developer": [
                "reviewing pull requests from the team",
                "architecting the new microservice",
                "mentoring junior developers",
                "optimizing database queries",
                "implementing the authentication system",
                "refactoring legacy code"
            ],
            "junior_developer": [
                "implementing the user registration feature",
                "writing unit tests for the payment module",
                "fixing bugs in the frontend components",
                "learning about the new framework",
                "working on the dashboard UI",
                "updating documentation"
            ],
            "devops_engineer": [
                "setting up CI/CD pipelines",
                "monitoring system performance",
                "deploying to production environment",
                "configuring load balancers",
                "updating security patches",
                "optimizing container orchestration"
            ],
            "qa_engineer": [
                "testing the new user interface",
                "automating regression tests",
                "validating API endpoints",
                "performing security testing",
                "creating test scenarios",
                "reporting and tracking bugs"
            ],
            "product_manager": [
                "gathering requirements from stakeholders",
                "prioritizing the product backlog",
                "analyzing user feedback",
                "coordinating with design team",
                "planning the next sprint",
                "reviewing market research"
            ]
        }

        # Get tasks for this role
        role_key = self.role.value if hasattr(self, 'role') else "senior_developer"
        tasks = role_tasks.get(role_key, role_tasks["senior_developer"])

        yesterday_task = random.choice(tasks)
        today_task = random.choice(tasks)

        # Personality-based variations
        if self.personality_type == PersonalityType.MINIMALIST:
            return f"Yesterday: {yesterday_task}. Today: {today_task}. No blockers."

        elif self.personality_type == PersonalityType.RAMBLER:
            return f"So yesterday I spent most of my time {yesterday_task}, which was quite interesting because we discovered some edge cases. Today I'll continue with {today_task} and dive deeper into the technical implementation details."

        elif self.personality_type == PersonalityType.PROBLEM_SOLVER:
            return f"Yesterday I was {yesterday_task}. Today I'm focusing on {today_task}. I think we should also consider how this impacts our overall architecture."

        elif self.personality_type == PersonalityType.QUESTIONER:
            return f"Yesterday I worked on {yesterday_task}. Today I'll be {today_task}. Quick question - are we aligned on the approach for this?"

        elif self.personality_type == PersonalityType.FACILITATOR:
            return f"Yesterday I focused on {yesterday_task} to support the team. Today I'll be {today_task} and ensuring we stay on track with our sprint goals."

        else:
            return f"Yesterday I worked on {yesterday_task}. Today I'll be {today_task}. Making good progress overall."
    
    def get_personality_description(self) -> str:
        """Get a description of this participant's personality"""
        traits = self.personality_traits
        
        descriptions = []
        
        if traits.verbosity > 0.7:
            descriptions.append("talkative")
        elif traits.verbosity < 0.3:
            descriptions.append("concise")
        
        if traits.technical_detail > 0.7:
            descriptions.append("detail-oriented")
        
        if traits.curiosity > 0.7:
            descriptions.append("inquisitive")
        
        if traits.interruption_tendency > 0.6:
            descriptions.append("eager to contribute")
        
        if traits.time_awareness > 0.8:
            descriptions.append("time-conscious")
        
        if not descriptions:
            descriptions.append("balanced")
        
        return f"{self.name} is {', '.join(descriptions)}"
    
    def _get_interruption_triggers(self) -> List[str]:
        """Get list of words/phrases that trigger interruptions for this personality"""
        if self.personality_type == PersonalityType.FACILITATOR:
            return ["time", "schedule", "meeting", "agenda"]
        elif self.personality_type == PersonalityType.QUESTIONER:
            return ["unclear", "confused", "not sure", "question"]
        elif self.personality_type == PersonalityType.PROBLEM_SOLVER:
            return ["issue", "problem", "bug", "error", "blocked"]
        else:
            return ["help", "question", "issue"]

    def _modify_interruption_probability(self,
                                       probability: float,
                                       content: str,
                                       context: ConversationContext) -> float:
        """Personality-specific modification of interruption probability"""
        if self.personality_type == PersonalityType.FACILITATOR:
            # Facilitators interrupt to keep meetings on track
            if "time" in content.lower() or len(content.split()) > 50:
                probability += 0.3
        elif self.personality_type == PersonalityType.MINIMALIST:
            # Minimalists rarely interrupt
            probability *= 0.5

        return probability

    def __str__(self) -> str:
        """String representation of the participant"""
        return f"{self.name} ({self.role.value}, {self.personality_type.value})"
