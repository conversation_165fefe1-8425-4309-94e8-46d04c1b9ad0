"""
Base Participant Class

Abstract base class for all meeting participants with personality traits
and role-specific behaviors.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from enum import Enum
import time


class Role(Enum):
    """Participant roles in daily standup meetings"""
    SCRUM_MASTER = "scrum_master"
    SENIOR_DEVELOPER = "senior_developer"
    JUNIOR_DEVELOPER = "junior_developer"
    PRODUCT_OWNER = "product_owner"
    DEVOPS_ENGINEER = "devops_engineer"
    QA_ENGINEER = "qa_engineer"


class PersonalityType(Enum):
    """Personality archetypes for meeting participants"""
    FACILITATOR = "facilitator"
    RAMBLER = "rambler"
    QUESTIONER = "questioner"
    MINIMALIST = "minimalist"
    PROBLEM_SOLVER = "problem_solver"
    STORYTELLER = "storyteller"
    INTERRUPTER = "interrupter"


@dataclass
class PersonalityTraits:
    """Personality traits that influence participant behavior"""
    
    # Core traits (0.0 - 1.0)
    verbosity: float = 0.5  # How much they talk
    technical_detail: float = 0.5  # Tendency to go into technical details
    interruption_tendency: float = 0.3  # Likelihood to interrupt others
    curiosity: float = 0.5  # Tendency to ask questions
    
    # Behavioral traits
    off_topic_probability: float = 0.3  # Likelihood to go off-topic
    follow_up_questions: float = 0.4  # Tendency to ask follow-up questions
    problem_solving_urge: float = 0.4  # Urge to solve problems during standup
    
    # Communication style
    speaking_speed: float = 1.0  # Relative speaking speed (words per minute)
    pause_frequency: float = 0.5  # How often they pause while speaking
    
    # Meeting behavior
    meeting_engagement: float = 0.7  # How engaged they are in the meeting
    time_awareness: float = 0.6  # Awareness of meeting time constraints


@dataclass
class ConversationContext:
    """Context information for generating responses"""
    meeting_phase: str  # opening, updates, blockers, closing
    previous_statements: List[str] = field(default_factory=list)
    current_topic: Optional[str] = None
    meeting_duration: float = 0.0  # minutes elapsed
    speaker_history: List[str] = field(default_factory=list)
    mentioned_topics: List[str] = field(default_factory=list)


class BaseParticipant(ABC):
    """
    Abstract base class for all meeting participants
    
    Defines the interface and common functionality for generating
    realistic participant responses in daily standup meetings.
    """
    
    def __init__(self, 
                 name: str, 
                 role: Role, 
                 personality_type: PersonalityType,
                 personality_traits: PersonalityTraits):
        """
        Initialize participant
        
        Args:
            name: Participant's name
            role: Their role in the team
            personality_type: Their personality archetype
            personality_traits: Specific personality trait values
        """
        self.name = name
        self.role = role
        self.personality_type = personality_type
        self.personality_traits = personality_traits
        
        # Conversation memory
        self.context_memory: List[str] = []
        self.topics_mentioned: List[str] = []
        self.questions_asked: List[str] = []
        
        # Meeting state
        self.has_given_update = False
        self.speaking_time_used = 0.0  # minutes
        self.interruption_count = 0
        
        # Response patterns specific to this participant
        self.response_patterns = self._initialize_response_patterns()
    
    @abstractmethod
    def _initialize_response_patterns(self) -> Dict[str, List[str]]:
        """Initialize response patterns specific to this personality"""
        pass
    
    @abstractmethod
    def generate_response(self, 
                         context: ConversationContext, 
                         prompt: str) -> str:
        """
        Generate a response based on context and prompt
        
        Args:
            context: Current conversation context
            prompt: Specific prompt or trigger for response
            
        Returns:
            Generated response text
        """
        pass
    
    def should_interrupt(self, 
                        current_speaker: str, 
                        content: str, 
                        context: ConversationContext) -> bool:
        """
        Determine if this participant should interrupt the current speaker
        
        Args:
            current_speaker: Name of current speaker
            content: What the current speaker is saying
            context: Current conversation context
            
        Returns:
            True if should interrupt, False otherwise
        """
        # Don't interrupt yourself
        if current_speaker == self.name:
            return False
        
        # Base interruption probability
        base_probability = self.personality_traits.interruption_tendency
        
        # Modify based on content and context
        interruption_probability = self._calculate_interruption_probability(
            content, context, base_probability
        )
        
        # Random decision based on probability
        import random
        return random.random() < interruption_probability
    
    def _calculate_interruption_probability(self, 
                                          content: str, 
                                          context: ConversationContext,
                                          base_probability: float) -> float:
        """Calculate interruption probability based on content and context"""
        
        probability = base_probability
        
        # Increase probability for certain triggers
        interruption_triggers = self._get_interruption_triggers()
        for trigger in interruption_triggers:
            if trigger.lower() in content.lower():
                probability += 0.2
        
        # Decrease probability if meeting is running long
        if context.meeting_duration > 15:  # 15 minutes
            probability *= 0.5
        
        # Personality-specific modifications
        probability = self._modify_interruption_probability(probability, content, context)
        
        return min(1.0, max(0.0, probability))
    
    @abstractmethod
    def _get_interruption_triggers(self) -> List[str]:
        """Get list of words/phrases that trigger interruptions for this personality"""
        pass
    
    @abstractmethod
    def _modify_interruption_probability(self, 
                                       probability: float, 
                                       content: str, 
                                       context: ConversationContext) -> float:
        """Personality-specific modification of interruption probability"""
        pass
    
    def get_speaking_duration(self, content: str) -> float:
        """
        Calculate realistic speaking duration for given content
        
        Args:
            content: Text content to be spoken
            
        Returns:
            Duration in seconds
        """
        # Base calculation: ~150 words per minute average speaking speed
        word_count = len(content.split())
        base_duration = (word_count / 150) * 60  # seconds
        
        # Adjust for personality traits
        speed_modifier = self.personality_traits.speaking_speed
        pause_modifier = 1 + (self.personality_traits.pause_frequency * 0.3)
        
        # Personality-specific adjustments
        duration = base_duration * pause_modifier / speed_modifier
        duration = self._adjust_speaking_duration(duration, content)
        
        return max(1.0, duration)  # Minimum 1 second
    
    @abstractmethod
    def _adjust_speaking_duration(self, duration: float, content: str) -> float:
        """Personality-specific adjustment of speaking duration"""
        pass
    
    def update_context_memory(self, statement: str, speaker: str):
        """Update participant's memory of the conversation"""
        self.context_memory.append(f"{speaker}: {statement}")
        
        # Keep only recent context (last 10 statements)
        if len(self.context_memory) > 10:
            self.context_memory = self.context_memory[-10:]
        
        # Extract and remember topics mentioned
        topics = self._extract_topics(statement)
        self.topics_mentioned.extend(topics)
    
    def _extract_topics(self, statement: str) -> List[str]:
        """Extract technical topics and keywords from statement"""
        # Simple keyword extraction - can be enhanced with NLP
        technical_keywords = [
            'api', 'database', 'frontend', 'backend', 'css', 'javascript',
            'python', 'deployment', 'testing', 'bug', 'feature', 'refactor',
            'authentication', 'authorization', 'performance', 'security'
        ]
        
        topics = []
        statement_lower = statement.lower()
        for keyword in technical_keywords:
            if keyword in statement_lower:
                topics.append(keyword)
        
        return topics
    
    def get_participant_info(self) -> Dict[str, Any]:
        """Get participant information for metadata"""
        return {
            'name': self.name,
            'role': self.role.value,
            'personality_type': self.personality_type.value,
            'personality_traits': {
                'verbosity': self.personality_traits.verbosity,
                'technical_detail': self.personality_traits.technical_detail,
                'interruption_tendency': self.personality_traits.interruption_tendency,
                'curiosity': self.personality_traits.curiosity,
                'off_topic_probability': self.personality_traits.off_topic_probability
            },
            'speaking_time_used': self.speaking_time_used,
            'interruption_count': self.interruption_count,
            'topics_mentioned': self.topics_mentioned
        }
    
    def reset_meeting_state(self):
        """Reset state for a new meeting"""
        self.has_given_update = False
        self.speaking_time_used = 0.0
        self.interruption_count = 0
        self.context_memory.clear()
        self.topics_mentioned.clear()
        self.questions_asked.clear()
    
    def __str__(self) -> str:
        return f"{self.name} ({self.role.value}, {self.personality_type.value})"
    
    def __repr__(self) -> str:
        return f"BaseParticipant(name='{self.name}', role={self.role}, personality={self.personality_type})"
