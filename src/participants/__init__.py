"""
Participants module for Daily Meeting Emulator

Contains participant classes with different personalities
and role-specific behaviors for realistic meeting simulation.
"""

from .base_participant import (
    BaseParticipant, PersonalityTraits, ConversationContext,
    Role, PersonalityType
)
from .simple_participant import SimpleParticipant

__all__ = [
    'BaseParticipant',
    'PersonalityTraits',
    'ConversationContext',
    'Role',
    'PersonalityType',
    'SimpleParticipant'
]