"""
Quota Checker for Gemini Models

Checks available quotas for different Gemini models and selects
the best model based on available quota.
"""

import logging
import google.generativeai as genai
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ModelQuota:
    """Information about a model's quota"""
    model_name: str
    requests_per_minute: int
    tokens_per_minute: int
    requests_per_day: int
    tokens_per_day: int
    available: bool = True
    error: Optional[str] = None


class QuotaChecker:
    """
    Checks quotas for different Gemini models and recommends the best one
    """
    
    # Available Gemini models to check
    MODELS_TO_CHECK = [
        "gemini-2.0-flash-exp",
        "gemini-1.5-flash",
        "gemini-1.5-flash-8b",
        "gemini-1.5-pro",
        "gemini-pro",
        "gemini-pro-vision"
    ]
    
    def __init__(self, api_key: str):
        """Initialize quota checker with API key"""
        self.api_key = api_key
        genai.configure(api_key=api_key)
        
    def check_all_models(self) -> Dict[str, ModelQuota]:
        """
        Check quotas for all available models
        
        Returns:
            Dictionary mapping model names to their quota information
        """
        quotas = {}
        
        print("🔍 Checking quotas for available Gemini models...")
        
        for model_name in self.MODELS_TO_CHECK:
            print(f"   📊 Checking {model_name}...")
            quota = self._check_model_quota(model_name)
            quotas[model_name] = quota
            
            if quota.available:
                print(f"   ✅ {model_name}: {quota.requests_per_minute} req/min, {quota.tokens_per_minute} tokens/min")
            else:
                print(f"   ❌ {model_name}: {quota.error}")
        
        return quotas
    
    def _check_model_quota(self, model_name: str) -> ModelQuota:
        """
        Check quota for a specific model
        
        Args:
            model_name: Name of the model to check
            
        Returns:
            ModelQuota object with quota information
        """
        try:
            # Try to create the model to check if it's available
            model = genai.GenerativeModel(model_name)
            
            # Try a simple test request to verify access
            test_response = model.generate_content(
                "Hello", 
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=10,
                    temperature=0.1
                )
            )
            
            if test_response.text:
                # Model is accessible, get quota info
                # Note: Actual quota limits vary by model and account
                quota_info = self._get_model_quota_limits(model_name)
                return ModelQuota(
                    model_name=model_name,
                    requests_per_minute=quota_info["rpm"],
                    tokens_per_minute=quota_info["tpm"],
                    requests_per_day=quota_info["rpd"],
                    tokens_per_day=quota_info["tpd"],
                    available=True
                )
            else:
                return ModelQuota(
                    model_name=model_name,
                    requests_per_minute=0,
                    tokens_per_minute=0,
                    requests_per_day=0,
                    tokens_per_day=0,
                    available=False,
                    error="Empty response from model"
                )
                
        except Exception as e:
            return ModelQuota(
                model_name=model_name,
                requests_per_minute=0,
                tokens_per_minute=0,
                requests_per_day=0,
                tokens_per_day=0,
                available=False,
                error=str(e)
            )
    
    def _get_model_quota_limits(self, model_name: str) -> Dict[str, int]:
        """
        Get quota limits for a specific model
        
        Note: These are typical limits - actual limits may vary by account
        """
        # Default quotas based on Gemini documentation
        quota_limits = {
            "gemini-2.0-flash-exp": {
                "rpm": 1000,  # requests per minute
                "tpm": 4000000,  # tokens per minute
                "rpd": 50000,  # requests per day
                "tpd": ********  # tokens per day
            },
            "gemini-1.5-flash": {
                "rpm": 1000,
                "tpm": 4000000,
                "rpd": 50000,
                "tpd": ********
            },
            "gemini-1.5-flash-8b": {
                "rpm": 2000,
                "tpm": 4000000,
                "rpd": 50000,
                "tpd": ********
            },
            "gemini-1.5-pro": {
                "rpm": 360,
                "tpm": 4000000,
                "rpd": 50000,
                "tpd": ********
            },
            "gemini-pro": {
                "rpm": 60,
                "tpm": 32000,
                "rpd": 1500,
                "tpd": 50000
            },
            "gemini-pro-vision": {
                "rpm": 60,
                "tpm": 32000,
                "rpd": 1500,
                "tpd": 50000
            }
        }
        
        return quota_limits.get(model_name, {
            "rpm": 60,
            "tpm": 32000,
            "rpd": 1500,
            "tpd": 50000
        })
    
    def get_best_model(self, quotas: Dict[str, ModelQuota]) -> Optional[str]:
        """
        Select the best model based on available quotas
        
        Args:
            quotas: Dictionary of model quotas
            
        Returns:
            Name of the best model, or None if no models available
        """
        available_models = [
            (name, quota) for name, quota in quotas.items() 
            if quota.available
        ]
        
        if not available_models:
            return None
        
        # Sort by requests per minute (higher is better for real-time generation)
        best_model = max(
            available_models, 
            key=lambda x: (x[1].requests_per_minute, x[1].tokens_per_minute)
        )
        
        return best_model[0]
    
    def print_quota_summary(self, quotas: Dict[str, ModelQuota], best_model: Optional[str]):
        """Print a summary of quota information"""
        print("\n📋 QUOTA SUMMARY:")
        print("=" * 60)
        
        available_count = sum(1 for q in quotas.values() if q.available)
        print(f"Available models: {available_count}/{len(quotas)}")
        
        if best_model:
            best_quota = quotas[best_model]
            print(f"\n🏆 RECOMMENDED MODEL: {best_model}")
            print(f"   📈 Requests per minute: {best_quota.requests_per_minute:,}")
            print(f"   🔤 Tokens per minute: {best_quota.tokens_per_minute:,}")
            print(f"   📅 Requests per day: {best_quota.requests_per_day:,}")
            print(f"   📊 Tokens per day: {best_quota.tokens_per_day:,}")
        else:
            print("\n❌ No available models found!")
        
        print("\n📊 ALL MODELS:")
        for name, quota in quotas.items():
            status = "✅" if quota.available else "❌"
            if quota.available:
                print(f"   {status} {name}: {quota.requests_per_minute:,} req/min")
            else:
                print(f"   {status} {name}: {quota.error}")
        
        print("=" * 60)


def check_quotas_and_select_model(api_key: str) -> Tuple[Optional[str], Dict[str, ModelQuota]]:
    """
    Convenience function to check quotas and select the best model
    
    Args:
        api_key: Gemini API key
        
    Returns:
        Tuple of (best_model_name, all_quotas)
    """
    checker = QuotaChecker(api_key)
    quotas = checker.check_all_models()
    best_model = checker.get_best_model(quotas)
    checker.print_quota_summary(quotas, best_model)
    
    return best_model, quotas


if __name__ == "__main__":
    import os
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY environment variable not set")
        exit(1)
    
    best_model, quotas = check_quotas_and_select_model(api_key)
    
    if best_model:
        print(f"\n🎯 Use this model: {best_model}")
    else:
        print("\n💥 No models available!")
