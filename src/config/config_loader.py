"""
Configuration Loader for Daily Meeting Emulator

Handles loading and validation of YAML configuration files
for meeting simulation parameters.
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Tuple, List, Optional

logger = logging.getLogger(__name__)


class ConfigurationError(Exception):
    """Raised when configuration is invalid or cannot be loaded"""
    pass


class ConfigLoader:
    """
    Loads and validates YAML configuration files for meeting simulation.
    
    Supports:
    - Loading from file paths
    - Configuration validation
    - Default value handling
    - Environment variable substitution
    """
    
    def __init__(self):
        """Initialize the configuration loader"""
        self.default_config_path = "config/default_meeting.yaml"
        
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from YAML file
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Dictionary containing the loaded configuration
            
        Raises:
            ConfigurationError: If file cannot be loaded or parsed
        """
        try:
            # Resolve path
            config_file = Path(config_path)
            if not config_file.is_absolute():
                config_file = Path.cwd() / config_file
                
            if not config_file.exists():
                raise ConfigurationError(f"Configuration file not found: {config_file}")
                
            # Load YAML content
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
                
            if not config_data:
                raise ConfigurationError(f"Configuration file is empty: {config_file}")
                
            # Apply environment variable substitution
            config_data = self._substitute_env_vars(config_data)
            
            # Validate configuration structure
            self._validate_structure(config_data)
            
            logger.info(f"Configuration loaded successfully from: {config_file}")
            return config_data
            
        except yaml.YAMLError as e:
            raise ConfigurationError(f"Invalid YAML in configuration file: {e}")
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {e}")
    
    def validate_config(self, config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate configuration data
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            # Validate required sections
            required_sections = ['meeting', 'participants', 'content', 'dialogue', 'output']
            for section in required_sections:
                if section not in config:
                    errors.append(f"Missing required section: {section}")
                    
            # Validate meeting section
            if 'meeting' in config:
                meeting_errors = self._validate_meeting_section(config['meeting'])
                errors.extend(meeting_errors)
                
            # Validate participants section
            if 'participants' in config:
                participant_errors = self._validate_participants_section(config['participants'])
                errors.extend(participant_errors)
                
            # Validate content section
            if 'content' in config:
                content_errors = self._validate_content_section(config['content'])
                errors.extend(content_errors)
                
            # Validate dialogue section
            if 'dialogue' in config:
                dialogue_errors = self._validate_dialogue_section(config['dialogue'])
                errors.extend(dialogue_errors)
                
            # Validate output section
            if 'output' in config:
                output_errors = self._validate_output_section(config['output'])
                errors.extend(output_errors)
                
        except Exception as e:
            errors.append(f"Validation error: {e}")
            
        return len(errors) == 0, errors
    
    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Substitute environment variables in configuration values
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Configuration with environment variables substituted
        """
        def substitute_value(value):
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                default_value = None
                
                # Handle default values: ${VAR:default}
                if ':' in env_var:
                    env_var, default_value = env_var.split(':', 1)
                    
                return os.getenv(env_var, default_value)
            elif isinstance(value, dict):
                return {k: substitute_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [substitute_value(item) for item in value]
            else:
                return value
                
        return substitute_value(config)
    
    def _validate_structure(self, config: Dict[str, Any]) -> None:
        """
        Validate basic configuration structure
        
        Args:
            config: Configuration dictionary to validate
            
        Raises:
            ConfigurationError: If structure is invalid
        """
        if not isinstance(config, dict):
            raise ConfigurationError("Configuration must be a dictionary")
            
        # Check for required top-level sections
        required_sections = ['meeting', 'participants', 'content']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            raise ConfigurationError(f"Missing required sections: {', '.join(missing_sections)}")
    
    def _validate_meeting_section(self, meeting: Dict[str, Any]) -> List[str]:
        """Validate meeting configuration section"""
        errors = []
        
        # Required fields
        required_fields = ['duration_minutes', 'participant_count', 'style']
        for field in required_fields:
            if field not in meeting:
                errors.append(f"Missing required meeting field: {field}")
                
        # Validate values
        if 'duration_minutes' in meeting:
            duration = meeting['duration_minutes']
            if not isinstance(duration, (int, float)) or duration <= 0:
                errors.append("duration_minutes must be a positive number")
                
        if 'participant_count' in meeting:
            count = meeting['participant_count']
            if not isinstance(count, int) or count < 2 or count > 20:
                errors.append("participant_count must be an integer between 2 and 20")
                
        if 'style' in meeting:
            style = meeting['style']
            valid_styles = ['strict', 'relaxed', 'chaotic', 'mixed']
            if style not in valid_styles:
                errors.append(f"style must be one of: {', '.join(valid_styles)}")
                
        return errors
    
    def _validate_participants_section(self, participants: List[Dict[str, Any]]) -> List[str]:
        """Validate participants configuration section"""
        errors = []
        
        if not isinstance(participants, list):
            errors.append("participants must be a list")
            return errors
            
        if len(participants) == 0:
            errors.append("participants list cannot be empty")
            return errors
            
        # Validate each participant
        for i, participant in enumerate(participants):
            if not isinstance(participant, dict):
                errors.append(f"Participant {i} must be a dictionary")
                continue
                
            # Required fields
            required_fields = ['name', 'role', 'personality']
            for field in required_fields:
                if field not in participant:
                    errors.append(f"Participant {i} missing required field: {field}")
                    
            # Validate traits if present
            if 'traits' in participant:
                trait_errors = self._validate_traits(participant['traits'], f"Participant {i}")
                errors.extend(trait_errors)
                
        return errors
    
    def _validate_content_section(self, content: Dict[str, Any]) -> List[str]:
        """Validate content configuration section"""
        errors = []
        
        # Validate off_topic_ratio
        if 'off_topic_ratio' in content:
            ratio = content['off_topic_ratio']
            if not isinstance(ratio, (int, float)) or ratio < 0 or ratio > 1:
                errors.append("off_topic_ratio must be a number between 0 and 1")
                
        return errors
    
    def _validate_dialogue_section(self, dialogue: Dict[str, Any]) -> List[str]:
        """Validate dialogue configuration section"""
        errors = []
        
        # Validate gemini settings if present
        if 'gemini' in dialogue:
            gemini = dialogue['gemini']
            if 'temperature' in gemini:
                temp = gemini['temperature']
                if not isinstance(temp, (int, float)) or temp < 0 or temp > 2:
                    errors.append("gemini.temperature must be a number between 0 and 2")
                    
        return errors
    
    def _validate_output_section(self, output: Dict[str, Any]) -> List[str]:
        """Validate output configuration section"""
        errors = []
        
        # Validate format if present
        if 'format' in output:
            format_type = output['format']
            valid_formats = ['timestamp', 'simple', 'detailed']
            if format_type not in valid_formats:
                errors.append(f"output.format must be one of: {', '.join(valid_formats)}")
                
        return errors
    
    def _validate_traits(self, traits: Dict[str, Any], context: str) -> List[str]:
        """Validate personality traits"""
        errors = []
        
        # All trait values should be between 0 and 1
        for trait_name, trait_value in traits.items():
            if not isinstance(trait_value, (int, float)) or trait_value < 0 or trait_value > 1:
                errors.append(f"{context} trait '{trait_name}' must be a number between 0 and 1")
                
        return errors
