#!/usr/bin/env python3
"""
Meeting Configuration Loader

Loads meeting configurations from YAML files for repeatable testing.
"""

import os
import yaml
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class ParticipantConfig:
    """Configuration for a meeting participant"""
    name: str
    role: str
    style: str
    expertise: str
    base_offtopic_tendency: float
    base_technical_tendency: float
    voice_model: str
    personality_traits: List[str]

@dataclass
class MeetingConfig:
    """Configuration for a meeting scenario"""
    name: str
    description: str
    max_messages: int
    offtopic_percent: float
    technical_percent: float
    participants: List[ParticipantConfig]

class MeetingConfigLoader:
    """Loads and manages meeting configurations"""
    
    def __init__(self, config_file: str = None):
        if config_file is None:
            # Try to find config file relative to this script or current directory
            script_dir = Path(__file__).parent
            config_file = script_dir / "meeting_configs.yaml"
            if not config_file.exists():
                config_file = "config/meeting_configs.yaml"
        self.config_file = config_file
        self.configs: Dict[str, MeetingConfig] = {}
        self.load_configs()
    
    def load_configs(self):
        """Load all meeting configurations from YAML file"""
        config_path = Path(self.config_file)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_file}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                raw_configs = yaml.safe_load(file)
            
            for config_name, config_data in raw_configs.items():
                self.configs[config_name] = self._parse_meeting_config(config_data)
                
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML configuration: {e}")
        except Exception as e:
            raise ValueError(f"Error loading configuration: {e}")
    
    def _parse_meeting_config(self, config_data: Dict[str, Any]) -> MeetingConfig:
        """Parse a single meeting configuration"""
        participants = []
        
        for participant_data in config_data.get('participants', []):
            participant = ParticipantConfig(
                name=participant_data['name'],
                role=participant_data['role'],
                style=participant_data['style'],
                expertise=participant_data['expertise'],
                base_offtopic_tendency=participant_data['base_offtopic_tendency'],
                base_technical_tendency=participant_data['base_technical_tendency'],
                voice_model=participant_data['voice_model'],
                personality_traits=participant_data.get('personality_traits', [])
            )
            participants.append(participant)
        
        return MeetingConfig(
            name=config_data['name'],
            description=config_data['description'],
            max_messages=config_data['max_messages'],
            offtopic_percent=config_data['offtopic_percent'],
            technical_percent=config_data['technical_percent'],
            participants=participants
        )
    
    def get_config(self, config_name: str) -> Optional[MeetingConfig]:
        """Get a specific meeting configuration"""
        return self.configs.get(config_name)
    
    def list_configs(self) -> List[str]:
        """List all available configuration names"""
        return list(self.configs.keys())
    
    def get_config_info(self, config_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific configuration"""
        config = self.get_config(config_name)
        if not config:
            return None
        
        return {
            'name': config.name,
            'description': config.description,
            'max_messages': config.max_messages,
            'offtopic_percent': config.offtopic_percent,
            'technical_percent': config.technical_percent,
            'num_participants': len(config.participants),
            'participants': [
                {
                    'name': p.name,
                    'role': p.role,
                    'style': p.style,
                    'expertise': p.expertise
                }
                for p in config.participants
            ]
        }
    
    def create_custom_config(self, 
                           name: str,
                           description: str,
                           max_messages: int,
                           offtopic_percent: float,
                           technical_percent: float,
                           participant_names: List[str]) -> MeetingConfig:
        """Create a custom configuration using default participant templates"""
        
        # Default participant templates
        default_participants = {
            "Alice": {
                "role": "Frontend Developer",
                "style": "concise",
                "expertise": "frontend",
                "base_offtopic_tendency": 0.2,
                "base_technical_tendency": 0.3,
                "voice_model": "aura-luna-en",
                "personality_traits": ["detail-oriented", "practical", "solution-focused"]
            },
            "Bob": {
                "role": "Backend Developer", 
                "style": "technical",
                "expertise": "backend",
                "base_offtopic_tendency": 0.1,
                "base_technical_tendency": 0.6,
                "voice_model": "aura-orion-en",
                "personality_traits": ["perfectionist", "loves technical discussions", "methodical"]
            },
            "Charlie": {
                "role": "Fullstack Developer",
                "style": "casual",
                "expertise": "fullstack", 
                "base_offtopic_tendency": 0.5,
                "base_technical_tendency": 0.4,
                "voice_model": "aura-arcas-en",
                "personality_traits": ["curious", "asks questions", "brings up tangents"]
            },
            "Diana": {
                "role": "DevOps Engineer",
                "style": "analytical",
                "expertise": "devops",
                "base_offtopic_tendency": 0.15,
                "base_technical_tendency": 0.7,
                "voice_model": "aura-asteria-en",
                "personality_traits": ["systems-thinking", "reliability-focused", "proactive"]
            },
            "Eve": {
                "role": "UX Designer",
                "style": "creative",
                "expertise": "design",
                "base_offtopic_tendency": 0.4,
                "base_technical_tendency": 0.2,
                "voice_model": "aura-stella-en",
                "personality_traits": ["user-empathy", "creative-thinking", "visual-oriented"]
            },
            "Frank": {
                "role": "QA Engineer",
                "style": "methodical",
                "expertise": "qa",
                "base_offtopic_tendency": 0.25,
                "base_technical_tendency": 0.5,
                "voice_model": "aura-perseus-en",
                "personality_traits": ["quality-focused", "detail-oriented", "risk-aware"]
            }
        }
        
        participants = []
        for participant_name in participant_names:
            if participant_name in default_participants:
                template = default_participants[participant_name]
                participant = ParticipantConfig(
                    name=participant_name,
                    role=template["role"],
                    style=template["style"],
                    expertise=template["expertise"],
                    base_offtopic_tendency=template["base_offtopic_tendency"],
                    base_technical_tendency=template["base_technical_tendency"],
                    voice_model=template["voice_model"],
                    personality_traits=template["personality_traits"]
                )
                participants.append(participant)
        
        return MeetingConfig(
            name=name,
            description=description,
            max_messages=max_messages,
            offtopic_percent=offtopic_percent,
            technical_percent=technical_percent,
            participants=participants
        )
    
    def save_config(self, config_name: str, config: MeetingConfig, 
                   output_file: Optional[str] = None):
        """Save a configuration to YAML file"""
        if output_file is None:
            output_file = f"config/custom_{config_name}.yaml"
        
        config_data = {
            config_name: {
                'name': config.name,
                'description': config.description,
                'max_messages': config.max_messages,
                'offtopic_percent': config.offtopic_percent,
                'technical_percent': config.technical_percent,
                'participants': [
                    {
                        'name': p.name,
                        'role': p.role,
                        'style': p.style,
                        'expertise': p.expertise,
                        'base_offtopic_tendency': p.base_offtopic_tendency,
                        'base_technical_tendency': p.base_technical_tendency,
                        'voice_model': p.voice_model,
                        'personality_traits': p.personality_traits
                    }
                    for p in config.participants
                ]
            }
        }
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as file:
            yaml.dump(config_data, file, default_flow_style=False, indent=2)

def main():
    """Test the configuration loader"""
    loader = MeetingConfigLoader()
    
    print("Available configurations:")
    for config_name in loader.list_configs():
        info = loader.get_config_info(config_name)
        print(f"  {config_name}: {info['name']} ({info['num_participants']} participants)")
        print(f"    {info['description']}")
        print()

if __name__ == "__main__":
    main()
