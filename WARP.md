# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Общие команды разработки

### Установка окружения
```bash
# Создание виртуального окружения
python -m venv .venv
source .venv/bin/activate  # Linux/Mac

# Установка зависимостей
pip install -r requirements.txt

# Настройка переменных окружения
cp .env.example .env
# Отредактируйте .env и добавьте GEMINI_API_KEY и опционально DEEPGRAM_API_KEY или ELEVENLABS_API_KEY
```

### Основные команды запуска
```bash
# Генерация простой текстовой встречи
python main.py generate --participants 3 --duration 5

# Генерация с голосовым синтезом (Deepgram)
python main.py generate --participants 3 --duration 5 --voice

# Быстрая генерация без речевых задержек
python main.py generate --participants 4 --duration 2 --fast

# Кастомная конфигурация
python main.py generate --participants 6 --duration 10 --style casual --off-topic-ratio 0.3

# Пакетная генерация встреч
python main.py batch --count 10 --output-dir data/generated/batch
```

### Демонстрационные скрипты
```bash
# Быстрая демонстрация с Deepgram
python quick_dynamic_demo.py

# Демонстрация с ElevenLabs голосами (если ELEVENLABS_API_KEY настроен)
python quick_dynamic_demo_epam_11labs.py

# EPAM версия демонстрации
python quick_dynamic_demo_epam.py
```

### Тестирование
```bash
# Тестирование конфигурации
python test_configs.py

# Тестирование ElevenLabs интеграции
python test_elevenlabs.py

# Запуск всех тестов
python -m pytest tests/

# Производительность и интеграционные тесты
python test_bot.py quick    # Быстрый тест без AI
python test_bot.py demo     # Интерактивная демонстрация
python test_bot.py check    # Проверка окружения
```

### Переключение между голосовыми моделями
```bash
# Утилита для переключения между голосовыми провайдерами
python switch_model.py
```

## Архитектура проекта

### Основная структура
Проект представляет собой AI-powered инструмент для симуляции ежедневных standup встреч с использованием Google Gemini 2.5 Flash для генерации диалогов и различных провайдеров (Deepgram Aura 2, ElevenLabs) для голосового синтеза.

### Ключевые компоненты:

#### `src/` - Основной исходный код
- **`dialogue/`** - Модуль интеграции с Gemini AI
  - `gemini_engine.py` - Основной движок для генерации диалогов
  
- **`meeting/`** - Ядро симуляции встреч
  - `meeting_simulator.py` - Основной симулятор встреч
  
- **`voice/`** - Система голосового синтеза
  - `deepgram_agent.py` - Интеграция с Deepgram Aura 2
  - `meeting_audio.py` - Управление аудио для встреч
  - `realtime_voice.py` - Система реального времени для голоса
  - `basic_audio.py`, `simple_audio.py` - Упрощенные аудио модули

- **`config/`** - Управление конфигурацией
  - `config_loader.py` - Загрузчик конфигураций

- **`meeting_moderator_bot/`** - Система модерации встреч
  - `core/` - Основная логика бота
    - `moderator.py` - Основной модератор
    - `conversation_monitor.py` - Мониторинг разговоров
    - `interruption_engine.py` - Движок прерываний
    - `topic_classifier.py` - Классификатор тем
    - `suggestion_engine.py` - Движок предложений
  - `integrations/` - Интеграции с внешними системами
    - `teams_bot.py` - Интеграция с Microsoft Teams
    - `webhook_integration.py` - Веб-хук интеграции

#### Корневые файлы
- **`main.py`** - Основная точка входа CLI интерфейса
- **`quick_dynamic_demo*.py`** - Демонстрационные скрипты
- **`switch_model.py`** - Утилита переключения голосовых моделей

### Конфигурационная система
Система использует YAML конфигурации для гибкой настройки:
- `config/default_meeting.yaml` - Основная конфигурация встреч
- `config/meeting_configs.yaml` - Дополнительные конфигурации
- Поддерживает настройку личностей участников, стилей встреч, голосовых параметров

### Типы личностей участников
- **facilitator** (фасилитатор) - Ясное авторитетное ведение встречи
- **rambler** (болтун) - Подробные длинные объяснения с отступлениями
- **questioner** (вопрошающий) - Любознательный, задает много вопросов
- **minimalist** (минималист) - Краткое, прямое общение
- **problem_solver** (решатель проблем) - Фокус на решениях
- **storyteller** (рассказчик) - Нарративное, выразительное общение
- **interrupter** (прерыватель) - Быстрые, энергичные вставки

### Интеграция с голосовыми сервисами

#### Deepgram Aura 2
- Высококачественный text-to-speech с SSML улучшениями
- Различные голоса для каждого типа личности
- Экспорт в WAV формат (24kHz, 16-bit)

#### ElevenLabs
- Более естественные голоса с лучшей интонацией
- Поддержка эмоций и многоязычность
- Автоматический выбор из доступных голосов
- Экспорт в MP3 формат

### Система качества
- Автоматическое измерение качества диалогов (обычно 0.8-0.9)
- Метрики производительности и аналитика
- Классификация контента на on-topic/off-topic

## API ключи и настройка

### Обязательные:
- `GEMINI_API_KEY` - Google Gemini API для генерации диалогов
  - Получить бесплатно: https://makersuite.google.com/app/apikey

### Опциональные (для голосового синтеза):
- `DEEPGRAM_API_KEY` - Deepgram Aura 2
  - Получить: https://console.deepgram.com/
- `ELEVENLABS_API_KEY` - ElevenLabs голосовой синтез
  - Получить: https://elevenlabs.io/

## Файлы вывода

### Текстовые транскрипты
- Расположение: `data/generated/`
- Формат: `meeting_YYYYMMDD_HHMMSS.txt`

### Аудио файлы
- Расположение: `data/audio/`
- Индивидуальные сегменты: `YYYYMMDD_HHMMSS_segment_XXX_ParticipantName.wav`
- Плейлисты: `YYYYMMDD_HHMMSS_playlist.txt`

## Особенности разработки

### Python версия
Требуется Python 3.12+ с поддержкой async/await

### Основные зависимости
- `google-generativeai` - Интеграция с Gemini
- `deepgram-sdk` - Голосовой синтез Deepgram
- `rich` - Красивый терминальный интерфейс
- `pyyaml` - Управление конфигурацией
- `click` - CLI интерфейс

### Тестирование
- `pytest` для unit тестов
- Интеграционные тесты через `test_bot.py`
- Производительное тестирование встроено

### Мониторинг производительности
Целевые показатели:
- Время отклика: <100ms (обычно ~75ms)
- Точность классификации: >90% (обычно 95%+)
- Пропускная способность: >10 msg/sec (обычно 15+)

## Советы по использованию

1. **Для разработки**: Используйте `--fast` режим для быстрого тестирования без речевых задержек
2. **Для демонстраций**: Включайте `--voice` для полного опыта с аудио
3. **Кастомизация**: Редактируйте `config/default_meeting.yaml` для настройки поведения
4. **Тестирование**: Начинайте с `python test_bot.py quick` для проверки основных функций
5. **ElevenLabs**: Предпочтительнее для высокого качества голоса, Deepgram для скорости

## Типичные ошибки

### API ключи не найдены
```bash
❌ GEMINI_API_KEY not found in environment variables
```
**Решение**: Убедитесь, что `.env` файл настроен правильно

### Проблемы с аудио
```bash
⚠️ Could not play audio (paplay not available)
```
**Решение**: Установите `pulseaudio-utils` или используйте другой аудио плеер

### Ошибки генерации
Если встречи генерируются некорректно, проверьте:
- Валидность API ключа Gemini
- Интернет соединение
- Лимиты API квоты
