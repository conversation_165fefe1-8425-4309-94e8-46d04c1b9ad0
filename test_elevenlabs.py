#!/usr/bin/env python3
"""
Test ElevenLabs integration
"""

import os
import asyncio
import tempfile
from pathlib import Path
import subprocess

# Импортируем ElevenLabsClient из нашего файла
import sys
sys.path.append('.')
from quick_dynamic_demo_epam_11labs import ElevenLabsClient

async def get_available_voices(client):
    """Get list of available voices"""
    import aiohttp

    url = "https://api.elevenlabs.io/v2/voices"
    headers = {
        "xi-api-key": client.api_key,
        "Content-Type": "application/json"
    }

    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers) as response:
            if response.status == 200:
                data = await response.json()
                return data.get("voices", [])
            else:
                error_text = await response.text()
                raise Exception(f"Failed to get voices: {response.status} - {error_text}")

async def test_elevenlabs():
    """Test ElevenLabs voice synthesis"""

    # Проверяем наличие API ключа
    api_key = os.getenv('ELEVENLABS_API_KEY')
    if not api_key:
        print("❌ ELEVENLABS_API_KEY not found in environment variables")
        print("Please set your ElevenLabs API key:")
        print("export ELEVENLABS_API_KEY='your_api_key_here'")
        return False

    try:
        # Создаем клиент
        client = ElevenLabsClient(api_key)
        print("✅ ElevenLabs client created")

        # Получаем список доступных голосов
        print("🔍 Getting available voices...")
        voices = await get_available_voices(client)

        if not voices:
            print("❌ No voices available")
            return False

        print(f"✅ Found {len(voices)} voices")

        # Показываем первые несколько голосов
        print("\n📋 Available voices:")
        for i, voice in enumerate(voices[:5]):  # Показываем первые 5
            print(f"  {i+1}. {voice['name']} (ID: {voice['voice_id']}) - {voice.get('category', 'unknown')}")

        # Используем первый доступный голос для теста
        test_voice = voices[0]
        voice_id = test_voice['voice_id']
        voice_name = test_voice['name']

        # Тестовый текст
        test_text = "Hello! This is a test of ElevenLabs voice synthesis for our meeting simulator."

        print(f"\n🎙️ Testing voice '{voice_name}' (ID: {voice_id})")
        print(f"📝 Text: '{test_text}'")

        # Тестируем разные модели
        models_to_test = [
            ("eleven_turbo_v2_5", "Turbo v2.5 (balanced)"),
            ("eleven_flash_v2_5", "Flash v2.5 (fast)"),
            ("eleven_multilingual_v2", "Multilingual v2 (quality)")
        ]

        for model_id, model_name in models_to_test:
            print(f"\n🤖 Testing model: {model_name}")
            try:
                # Генерируем аудио
                audio_data = await client.text_to_speech(test_text, voice_id, model_id=model_id)
                print(f"✅ Audio generated: {len(audio_data)} bytes")

                # Сохраняем во временный файл
                with tempfile.NamedTemporaryFile(suffix=f"_{model_id}.mp3", delete=False) as temp_file:
                    temp_file.write(audio_data)
                    temp_filename = temp_file.name

                print(f"💾 Audio saved to: {temp_filename}")

                # Воспроизводим (если доступно)
                try:
                    subprocess.run(['paplay', temp_filename], check=True, capture_output=True)
                    print("🔊 Audio played successfully")
                except (subprocess.CalledProcessError, FileNotFoundError):
                    print("⚠️ Could not play audio (paplay not available)")

                # Удаляем временный файл
                Path(temp_filename).unlink()
                print("🗑️ Temporary file cleaned up")

            except Exception as e:
                print(f"❌ Error with {model_name}: {e}")
                continue
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing ElevenLabs Integration")
    print("=" * 40)
    
    success = asyncio.run(test_elevenlabs())
    
    if success:
        print("\n✅ ElevenLabs integration test passed!")
        print("You can now run the demo with voice synthesis enabled.")
    else:
        print("\n❌ ElevenLabs integration test failed!")
        print("Please check your API key and internet connection.")
