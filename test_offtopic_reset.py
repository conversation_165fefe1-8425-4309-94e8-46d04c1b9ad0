#!/usr/bin/env python3
"""
Test Off-topic Counter Reset Logic

Tests that the off-topic counter resets after moderator intervention.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from quick_dynamic_demo import QuickD<PERSON>Demo

def test_offtopic_counter_reset():
    """Test off-topic counter reset after moderator intervention"""
    print("🧪 Testing Off-topic Counter Reset Logic")
    print("="*50)
    
    demo = QuickDynamicDemo()
    demo.context["phase"] = "discussion"
    
    print("📋 Initial state:")
    print(f"   Counter: {demo.context['consecutive_offtopic']}")
    print()
    
    # Simulate consecutive off-topic messages
    print("📝 Simulating consecutive off-topic messages:")
    
    # First off-topic message
    demo.update_offtopic_counter("off_topic")
    should_intervene_1 = demo.should_moderator_intervene("off_topic")
    print(f"   1st off-topic: counter={demo.context['consecutive_offtopic']}, intervene={should_intervene_1}")
    
    # Second off-topic message (should trigger intervention)
    demo.update_offtopic_counter("off_topic")
    should_intervene_2 = demo.should_moderator_intervene("off_topic")
    print(f"   2nd off-topic: counter={demo.context['consecutive_offtopic']}, intervene={should_intervene_2}")
    
    # Simulate moderator intervention (manual reset)
    print("\n🤖 Moderator intervenes:")
    demo.context["consecutive_offtopic"] = 0
    print(f"   Counter after intervention: {demo.context['consecutive_offtopic']}")
    
    # Next off-topic message should not trigger intervention
    demo.update_offtopic_counter("off_topic")
    should_intervene_3 = demo.should_moderator_intervene("off_topic")
    print(f"   Next off-topic: counter={demo.context['consecutive_offtopic']}, intervene={should_intervene_3}")
    
    print("\n✅ Test Results:")
    print(f"   1st message intervention: {should_intervene_1} (expected: False)")
    print(f"   2nd message intervention: {should_intervene_2} (expected: True)")
    print(f"   After reset intervention: {should_intervene_3} (expected: False)")
    
    # Verify results
    success = (
        not should_intervene_1 and  # First should not intervene
        should_intervene_2 and      # Second should intervene
        not should_intervene_3      # After reset should not intervene
    )
    
    if success:
        print("   🎉 All tests passed!")
    else:
        print("   ❌ Some tests failed!")
    
    return success

def test_technical_vs_offtopic():
    """Test that technical messages don't affect off-topic counter"""
    print("\n🧪 Testing Technical vs Off-topic Counter")
    print("="*50)
    
    demo = QuickDynamicDemo()
    demo.context["phase"] = "discussion"
    
    # Mix of technical and off-topic messages
    test_sequence = [
        ("off_topic", False),      # 1st off-topic
        ("technical", False),      # Technical (doesn't reset, but doesn't increment)
        ("off_topic", True),       # 2nd off-topic (should trigger)
    ]
    
    print("📝 Testing mixed message sequence:")
    for i, (msg_type, expected_intervention) in enumerate(test_sequence, 1):
        demo.update_offtopic_counter(msg_type)
        should_intervene = demo.should_moderator_intervene(msg_type)
        
        status = "✅" if should_intervene == expected_intervention else "❌"
        print(f"   {status} {i}. {msg_type}: counter={demo.context['consecutive_offtopic']}, intervene={should_intervene}")
    
    print()

def test_standup_vs_discussion_phases():
    """Test different intervention logic for standup vs discussion phases"""
    print("🧪 Testing Standup vs Discussion Phase Logic")
    print("="*50)
    
    demo = QuickDynamicDemo()
    
    # Test standup phase
    demo.context["phase"] = "standup"
    demo.context["consecutive_offtopic"] = 0
    
    demo.update_offtopic_counter("off_topic")
    standup_intervention = demo.should_moderator_intervene("off_topic")
    
    print(f"📋 Standup phase:")
    print(f"   1st off-topic: counter={demo.context['consecutive_offtopic']}, intervene={standup_intervention}")
    
    # Test discussion phase
    demo.context["phase"] = "discussion"
    demo.context["consecutive_offtopic"] = 0
    
    demo.update_offtopic_counter("off_topic")
    discussion_intervention_1 = demo.should_moderator_intervene("off_topic")
    
    demo.update_offtopic_counter("off_topic")
    discussion_intervention_2 = demo.should_moderator_intervene("off_topic")
    
    print(f"💬 Discussion phase:")
    print(f"   1st off-topic: counter=1, intervene={discussion_intervention_1}")
    print(f"   2nd off-topic: counter=2, intervene={discussion_intervention_2}")
    
    print(f"\n✅ Results:")
    print(f"   Standup 1st off-topic: {standup_intervention} (expected: True)")
    print(f"   Discussion 1st off-topic: {discussion_intervention_1} (expected: False)")
    print(f"   Discussion 2nd off-topic: {discussion_intervention_2} (expected: True)")

def main():
    print("🔄 Off-topic Counter Reset Tests")
    print("="*60)
    
    test_offtopic_counter_reset()
    test_technical_vs_offtopic()
    test_standup_vs_discussion_phases()
    
    print("\n✅ All counter reset tests completed!")

if __name__ == "__main__":
    main()
