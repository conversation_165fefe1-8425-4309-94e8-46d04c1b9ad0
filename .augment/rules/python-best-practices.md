# Python Best Practices and Guidelines

**Type**: Always
**Description**: Python development best practices, coding standards, and project structure guidelines

## Project Structure and Environment

- Always use `.venv` as the virtual environment directory name for Python projects
- Install Python packages in the `.venv` virtual environment using appropriate package managers
- Add `.env` files to `.gitignore` to keep sensitive environment variables out of version control
- Prefer repositories to be private, not public
- Implement bot functionality as separate services/modules in dedicated folders within projects for later extraction and integration into Teams bots

## Package Management

- Always use package managers (pip, poetry, conda) for dependency management instead of manually editing requirements.txt or pyproject.toml
- Use `pip install`, `pip uninstall`, `poetry add`, `poetry remove`, or `conda install/remove` commands
- Never manually edit package configuration files unless performing complex configuration changes that cannot be accomplished through package manager commands

## Code Quality and Standards

- Always use English language for code comments, pull requests, and commits
- Follow PEP 8 style guidelines for Python code
- Use type hints where appropriate to improve code readability and maintainability
- Write comprehensive docstrings for functions, classes, and modules
- Use meaningful variable and function names that clearly describe their purpose
- Functions should start with verbs to indicate actions

## Testing

- Write unit tests for all new code functionality
- Suggest writing and running tests after implementing new features
- Use pytest as the preferred testing framework over unittest
- Ensure tests cover edge cases and error conditions
- Run tests before committing code changes

## AI Integration

- Use Google's VertexAI platform for accessing Gemini models instead of direct API access
- Always use Gemini API for AI-powered functionality
- Prefer gemini-2.5-flash model for AI functionality
- Use max_tokens=4000 for AI model configurations
- Generate dialogue phrases as if they are spoken in real-time to manage API usage efficiently
- Implement proper error handling for API quota limits (429 errors)
- Use dynamic AI-generated content rather than repetitive phrases for more natural interactions

## Voice Integration

- Use ElevenLabs instead of Deepgram for voice synthesis
- Prefer ElevenLabs v3 model over v2 for voice synthesis
- Implement proper audio device handling for voice/audio functionality

## Meeting Simulation Features

- Externalize meeting configurations (participant count, characteristics, voices) to configuration files for repeatability and testing
- Implement proper conversation flow where questions are answered before moving to next phase
- Use AI-generated moderator responses rather than pre-written templates
- Implement proper participant selection and response routing
- Support multiple languages with appropriate voice synthesis without accents
- Display meeting summaries showing created Jira tickets and scheduled technical meetings

## Error Handling and Debugging

- Implement comprehensive error handling for API calls and external services
- Use logging to track application behavior and debug issues
- Handle quota exhaustion gracefully with fallback mechanisms
- Avoid infinite loops in bot interactions

## Security

- Store API keys and sensitive configuration in environment variables
- Never commit API keys or secrets to version control
- Use proper authentication methods for external service integrations

## Performance

- Implement efficient token usage for AI model interactions
- Use appropriate caching strategies for API responses
- Monitor and optimize API quota usage

## Documentation

- Maintain clear README files with setup and usage instructions
- Document API integrations and configuration requirements
- Provide examples of configuration files and usage patterns
